[{"C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\app\\layout.tsx": "1", "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\app\\page.tsx": "2", "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\lib\\constants.ts": "3", "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\lib\\utils.ts": "4", "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\data\\content.ts": "5", "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\types\\index.ts": "6", "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\components\\sections\\HeroSection.tsx": "7", "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\components\\sections\\ValuePropositionSection.tsx": "8"}, {"size": 689, "mtime": 1752768982291, "results": "9", "hashOfConfig": "10"}, {"size": 1010, "mtime": 1752769879091, "results": "11", "hashOfConfig": "10"}, {"size": 550, "mtime": 1752769294386, "results": "12", "hashOfConfig": "10"}, {"size": 166, "mtime": 1752769335996, "results": "13", "hashOfConfig": "10"}, {"size": 1696, "mtime": 1752769483700, "results": "14", "hashOfConfig": "10"}, {"size": 552, "mtime": 1752769493152, "results": "15", "hashOfConfig": "10"}, {"size": 4171, "mtime": 1752769929786, "results": "16", "hashOfConfig": "10"}, {"size": 2259, "mtime": 1752769847269, "results": "17", "hashOfConfig": "10"}, {"filePath": "18", "messages": "19", "suppressedMessages": "20", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1kfznf5", {"filePath": "21", "messages": "22", "suppressedMessages": "23", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\app\\layout.tsx", [], [], "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\app\\page.tsx", [], [], "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\lib\\constants.ts", [], [], "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\lib\\utils.ts", [], [], "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\data\\content.ts", [], [], "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\types\\index.ts", [], [], "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\components\\sections\\HeroSection.tsx", [], [], "C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\components\\sections\\ValuePropositionSection.tsx", [], []]