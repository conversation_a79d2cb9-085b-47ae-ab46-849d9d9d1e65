(()=>{var a={};a.id=974,a.ids=[974],a.modules={132:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(5239),e=c(8088),f=c(7220),g=c(1289),h=c(6191),i=c(4823),j=c(1998),k=c(2603),l=c(4649),m=c(2781),n=c(2602),o=c(1268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(6713),u=c(3365),v=c(1454),w=c(7778),x=c(6143),y=c(9105),z=c(8171),A=c(6439),B=c(6133),C=c.n(B),D=c(893),E=c(2836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,1204)),"C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\app\\page.tsx"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,4431)),"C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,6133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,9868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,9615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\app\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},440:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e});var d=c(1658);let e=async a=>[{type:"image/x-icon",sizes:"16x16",url:(0,d.fillMetadataSegment)(".",await a.params,"favicon.ico")+""}]},582:(a,b,c)=>{Promise.resolve().then(c.bind(c,2234)),Promise.resolve().then(c.bind(c,3437))},846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},1135:()=>{},1204:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>g});var d=c(7413),e=c(2234),f=c(3437);function g(){return(0,d.jsxs)("main",{children:[(0,d.jsx)(e.default,{}),(0,d.jsx)(f.default,{}),(0,d.jsx)("div",{id:"packages",className:"min-h-screen bg-white flex items-center justify-center",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("h2",{className:"text-3xl font-heading font-bold text-neutral-900 mb-4",children:"Packages Section"}),(0,d.jsx)("p",{className:"text-neutral-600",children:"Coming soon in Phase 2.2"})]})}),(0,d.jsx)("div",{id:"booking-form",className:"min-h-screen bg-neutral-50 flex items-center justify-center",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("h2",{className:"text-3xl font-heading font-bold text-neutral-900 mb-4",children:"Booking Form"}),(0,d.jsx)("p",{className:"text-neutral-600",children:"Coming soon in future phases"})]})})]})}},2028:()=>{},2234:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(1369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\D\\\\Demo\\\\Travel Website\\\\blissful-trails-homepage\\\\src\\\\components\\\\sections\\\\HeroSection.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\components\\sections\\HeroSection.tsx","default")},2312:(a,b,c)=>{"use strict";let d;c.d(b,{P:()=>fj});var e=c(3210);let f=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],g=new Set(f),h=a=>180*a/Math.PI,i=a=>k(h(Math.atan2(a[1],a[0]))),j={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:a=>(Math.abs(a[0])+Math.abs(a[3]))/2,rotate:i,rotateZ:i,skewX:a=>h(Math.atan(a[1])),skewY:a=>h(Math.atan(a[2])),skew:a=>(Math.abs(a[1])+Math.abs(a[2]))/2},k=a=>((a%=360)<0&&(a+=360),a),l=a=>Math.sqrt(a[0]*a[0]+a[1]*a[1]),m=a=>Math.sqrt(a[4]*a[4]+a[5]*a[5]),n={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:l,scaleY:m,scale:a=>(l(a)+m(a))/2,rotateX:a=>k(h(Math.atan2(a[6],a[5]))),rotateY:a=>k(h(Math.atan2(-a[2],a[0]))),rotateZ:i,rotate:i,skewX:a=>h(Math.atan(a[4])),skewY:a=>h(Math.atan(a[1])),skew:a=>(Math.abs(a[1])+Math.abs(a[4]))/2};function o(a){return+!!a.includes("scale")}function p(a,b){let c,d;if(!a||"none"===a)return o(b);let e=a.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(e)c=n,d=e;else{let b=a.match(/^matrix\(([-\d.e\s,]+)\)$/u);c=j,d=b}if(!d)return o(b);let f=c[b],g=d[1].split(",").map(q);return"function"==typeof f?f(g):g[f]}function q(a){return parseFloat(a.trim())}let r=a=>b=>"string"==typeof b&&b.startsWith(a),s=r("--"),t=r("var(--"),u=a=>!!t(a)&&v.test(a.split("/*")[0].trim()),v=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu;function w({top:a,left:b,right:c,bottom:d}){return{x:{min:b,max:c},y:{min:a,max:d}}}let x=(a,b,c)=>a+(b-a)*c;function y(a){return void 0===a||1===a}function z({scale:a,scaleX:b,scaleY:c}){return!y(a)||!y(b)||!y(c)}function A(a){return z(a)||B(a)||a.z||a.rotate||a.rotateX||a.rotateY||a.skewX||a.skewY}function B(a){var b,c;return(b=a.x)&&"0%"!==b||(c=a.y)&&"0%"!==c}function C(a,b,c,d,e){return void 0!==e&&(a=d+e*(a-d)),d+c*(a-d)+b}function D(a,b=0,c=1,d,e){a.min=C(a.min,b,c,d,e),a.max=C(a.max,b,c,d,e)}function E(a,{x:b,y:c}){D(a.x,b.translate,b.scale,b.originPoint),D(a.y,c.translate,c.scale,c.originPoint)}function F(a,b){a.min=a.min+b,a.max=a.max+b}function G(a,b,c,d,e=.5){let f=x(a.min,a.max,e);D(a,b,c,f,d)}function H(a,b){G(a.x,b.x,b.scaleX,b.scale,b.originX),G(a.y,b.y,b.scaleY,b.scale,b.originY)}function I(a,b){return w(function(a,b){if(!b)return a;let c=b({x:a.left,y:a.top}),d=b({x:a.right,y:a.bottom});return{top:c.y,left:c.x,bottom:d.y,right:d.x}}(a.getBoundingClientRect(),b))}let J=new Set(["width","height","top","left","right","bottom",...f]),K=(a,b,c)=>c>b?b:c<a?a:c,L={test:a=>"number"==typeof a,parse:parseFloat,transform:a=>a},M={...L,transform:a=>K(0,1,a)},N={...L,default:1},O=a=>({test:b=>"string"==typeof b&&b.endsWith(a)&&1===b.split(" ").length,parse:parseFloat,transform:b=>`${b}${a}`}),P=O("deg"),Q=O("%"),R=O("px"),S=O("vh"),T=O("vw"),U={...Q,parse:a=>Q.parse(a)/100,transform:a=>Q.transform(100*a)},V=a=>b=>b.test(a),W=[L,R,Q,P,T,S,{test:a=>"auto"===a,parse:a=>a}],X=a=>W.find(V(a)),Y=()=>{},Z=()=>{},$=a=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(a),_=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,aa=a=>a===L||a===R,ab=new Set(["x","y","z"]),ac=f.filter(a=>!ab.has(a)),ad={width:({x:a},{paddingLeft:b="0",paddingRight:c="0"})=>a.max-a.min-parseFloat(b)-parseFloat(c),height:({y:a},{paddingTop:b="0",paddingBottom:c="0"})=>a.max-a.min-parseFloat(b)-parseFloat(c),top:(a,{top:b})=>parseFloat(b),left:(a,{left:b})=>parseFloat(b),bottom:({y:a},{top:b})=>parseFloat(b)+(a.max-a.min),right:({x:a},{left:b})=>parseFloat(b)+(a.max-a.min),x:(a,{transform:b})=>p(b,"x"),y:(a,{transform:b})=>p(b,"y")};ad.translateX=ad.x,ad.translateY=ad.y;let ae=a=>a,af={},ag=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],ah={value:null,addProjectionMetrics:null};function ai(a,b){let c=!1,d=!0,e={delta:0,timestamp:0,isProcessing:!1},f=()=>c=!0,g=ag.reduce((a,c)=>(a[c]=function(a,b){let c=new Set,d=new Set,e=!1,f=!1,g=new WeakSet,h={delta:0,timestamp:0,isProcessing:!1},i=0;function j(b){g.has(b)&&(k.schedule(b),a()),i++,b(h)}let k={schedule:(a,b=!1,f=!1)=>{let h=f&&e?c:d;return b&&g.add(a),h.has(a)||h.add(a),a},cancel:a=>{d.delete(a),g.delete(a)},process:a=>{if(h=a,e){f=!0;return}e=!0,[c,d]=[d,c],c.forEach(j),b&&ah.value&&ah.value.frameloop[b].push(i),i=0,c.clear(),e=!1,f&&(f=!1,k.process(a))}};return k}(f,b?c:void 0),a),{}),{setup:h,read:i,resolveKeyframes:j,preUpdate:k,update:l,preRender:m,render:n,postRender:o}=g,p=()=>{let f=af.useManualTiming?e.timestamp:performance.now();c=!1,af.useManualTiming||(e.delta=d?1e3/60:Math.max(Math.min(f-e.timestamp,40),1)),e.timestamp=f,e.isProcessing=!0,h.process(e),i.process(e),j.process(e),k.process(e),l.process(e),m.process(e),n.process(e),o.process(e),e.isProcessing=!1,c&&b&&(d=!1,a(p))};return{schedule:ag.reduce((b,f)=>{let h=g[f];return b[f]=(b,f=!1,g=!1)=>(!c&&(c=!0,d=!0,e.isProcessing||a(p)),h.schedule(b,f,g)),b},{}),cancel:a=>{for(let b=0;b<ag.length;b++)g[ag[b]].cancel(a)},state:e,steps:g}}let{schedule:aj,cancel:ak,state:al,steps:am}=ai("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:ae,!0),an=new Set,ao=!1,ap=!1,aq=!1;function ar(){if(ap){let a=Array.from(an).filter(a=>a.needsMeasurement),b=new Set(a.map(a=>a.element)),c=new Map;b.forEach(a=>{let b=function(a){let b=[];return ac.forEach(c=>{let d=a.getValue(c);void 0!==d&&(b.push([c,d.get()]),d.set(+!!c.startsWith("scale")))}),b}(a);b.length&&(c.set(a,b),a.render())}),a.forEach(a=>a.measureInitialState()),b.forEach(a=>{a.render();let b=c.get(a);b&&b.forEach(([b,c])=>{a.getValue(b)?.set(c)})}),a.forEach(a=>a.measureEndState()),a.forEach(a=>{void 0!==a.suspendedScrollY&&window.scrollTo(0,a.suspendedScrollY)})}ap=!1,ao=!1,an.forEach(a=>a.complete(aq)),an.clear()}function as(){an.forEach(a=>{a.readKeyframes(),a.needsMeasurement&&(ap=!0)})}class at{constructor(a,b,c,d,e,f=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...a],this.onComplete=b,this.name=c,this.motionValue=d,this.element=e,this.isAsync=f}scheduleResolve(){this.state="scheduled",this.isAsync?(an.add(this),ao||(ao=!0,aj.read(as),aj.resolveKeyframes(ar))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:a,name:b,element:c,motionValue:d}=this;if(null===a[0]){let e=d?.get(),f=a[a.length-1];if(void 0!==e)a[0]=e;else if(c&&b){let d=c.readValue(b,f);null!=d&&(a[0]=d)}void 0===a[0]&&(a[0]=f),d&&void 0===e&&d.set(a[0])}for(let b=1;b<a.length;b++)a[b]??(a[b]=a[b-1])}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(a=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,a),an.delete(this)}cancel(){"scheduled"===this.state&&(an.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}let au=a=>/^0[^.\s]+$/u.test(a),av=a=>Math.round(1e5*a)/1e5,aw=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,ax=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,ay=(a,b)=>c=>!!("string"==typeof c&&ax.test(c)&&c.startsWith(a)||b&&null!=c&&Object.prototype.hasOwnProperty.call(c,b)),az=(a,b,c)=>d=>{if("string"!=typeof d)return d;let[e,f,g,h]=d.match(aw);return{[a]:parseFloat(e),[b]:parseFloat(f),[c]:parseFloat(g),alpha:void 0!==h?parseFloat(h):1}},aA={...L,transform:a=>Math.round(K(0,255,a))},aB={test:ay("rgb","red"),parse:az("red","green","blue"),transform:({red:a,green:b,blue:c,alpha:d=1})=>"rgba("+aA.transform(a)+", "+aA.transform(b)+", "+aA.transform(c)+", "+av(M.transform(d))+")"},aC={test:ay("#"),parse:function(a){let b="",c="",d="",e="";return a.length>5?(b=a.substring(1,3),c=a.substring(3,5),d=a.substring(5,7),e=a.substring(7,9)):(b=a.substring(1,2),c=a.substring(2,3),d=a.substring(3,4),e=a.substring(4,5),b+=b,c+=c,d+=d,e+=e),{red:parseInt(b,16),green:parseInt(c,16),blue:parseInt(d,16),alpha:e?parseInt(e,16)/255:1}},transform:aB.transform},aD={test:ay("hsl","hue"),parse:az("hue","saturation","lightness"),transform:({hue:a,saturation:b,lightness:c,alpha:d=1})=>"hsla("+Math.round(a)+", "+Q.transform(av(b))+", "+Q.transform(av(c))+", "+av(M.transform(d))+")"},aE={test:a=>aB.test(a)||aC.test(a)||aD.test(a),parse:a=>aB.test(a)?aB.parse(a):aD.test(a)?aD.parse(a):aC.parse(a),transform:a=>"string"==typeof a?a:a.hasOwnProperty("red")?aB.transform(a):aD.transform(a),getAnimatableNone:a=>{let b=aE.parse(a);return b.alpha=0,aE.transform(b)}},aF=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,aG="number",aH="color",aI=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function aJ(a){let b=a.toString(),c=[],d={color:[],number:[],var:[]},e=[],f=0,g=b.replace(aI,a=>(aE.test(a)?(d.color.push(f),e.push(aH),c.push(aE.parse(a))):a.startsWith("var(")?(d.var.push(f),e.push("var"),c.push(a)):(d.number.push(f),e.push(aG),c.push(parseFloat(a))),++f,"${}")).split("${}");return{values:c,split:g,indexes:d,types:e}}function aK(a){return aJ(a).values}function aL(a){let{split:b,types:c}=aJ(a),d=b.length;return a=>{let e="";for(let f=0;f<d;f++)if(e+=b[f],void 0!==a[f]){let b=c[f];b===aG?e+=av(a[f]):b===aH?e+=aE.transform(a[f]):e+=a[f]}return e}}let aM=a=>"number"==typeof a?0:aE.test(a)?aE.getAnimatableNone(a):a,aN={test:function(a){return isNaN(a)&&"string"==typeof a&&(a.match(aw)?.length||0)+(a.match(aF)?.length||0)>0},parse:aK,createTransformer:aL,getAnimatableNone:function(a){let b=aK(a);return aL(a)(b.map(aM))}},aO=new Set(["brightness","contrast","saturate","opacity"]);function aP(a){let[b,c]=a.slice(0,-1).split("(");if("drop-shadow"===b)return a;let[d]=c.match(aw)||[];if(!d)return a;let e=c.replace(d,""),f=+!!aO.has(b);return d!==c&&(f*=100),b+"("+f+e+")"}let aQ=/\b([a-z-]*)\(.*?\)/gu,aR={...aN,getAnimatableNone:a=>{let b=a.match(aQ);return b?b.map(aP).join(" "):a}},aS={...L,transform:Math.round},aT={borderWidth:R,borderTopWidth:R,borderRightWidth:R,borderBottomWidth:R,borderLeftWidth:R,borderRadius:R,radius:R,borderTopLeftRadius:R,borderTopRightRadius:R,borderBottomRightRadius:R,borderBottomLeftRadius:R,width:R,maxWidth:R,height:R,maxHeight:R,top:R,right:R,bottom:R,left:R,padding:R,paddingTop:R,paddingRight:R,paddingBottom:R,paddingLeft:R,margin:R,marginTop:R,marginRight:R,marginBottom:R,marginLeft:R,backgroundPositionX:R,backgroundPositionY:R,rotate:P,rotateX:P,rotateY:P,rotateZ:P,scale:N,scaleX:N,scaleY:N,scaleZ:N,skew:P,skewX:P,skewY:P,distance:R,translateX:R,translateY:R,translateZ:R,x:R,y:R,z:R,perspective:R,transformPerspective:R,opacity:M,originX:U,originY:U,originZ:R,zIndex:aS,fillOpacity:M,strokeOpacity:M,numOctaves:aS},aU={...aT,color:aE,backgroundColor:aE,outlineColor:aE,fill:aE,stroke:aE,borderColor:aE,borderTopColor:aE,borderRightColor:aE,borderBottomColor:aE,borderLeftColor:aE,filter:aR,WebkitFilter:aR},aV=a=>aU[a];function aW(a,b){let c=aV(a);return c!==aR&&(c=aN),c.getAnimatableNone?c.getAnimatableNone(b):void 0}let aX=new Set(["auto","none","0"]);class aY extends at{constructor(a,b,c,d,e){super(a,b,c,d,e,!0)}readKeyframes(){let{unresolvedKeyframes:a,element:b,name:c}=this;if(!b||!b.current)return;super.readKeyframes();for(let c=0;c<a.length;c++){let d=a[c];if("string"==typeof d&&u(d=d.trim())){let e=function a(b,c,d=1){Z(d<=4,`Max CSS variable fallback depth detected in property "${b}". This may indicate a circular fallback dependency.`,"max-css-var-depth");let[e,f]=function(a){let b=_.exec(a);if(!b)return[,];let[,c,d,e]=b;return[`--${c??d}`,e]}(b);if(!e)return;let g=window.getComputedStyle(c).getPropertyValue(e);if(g){let a=g.trim();return $(a)?parseFloat(a):a}return u(f)?a(f,c,d+1):f}(d,b.current);void 0!==e&&(a[c]=e),c===a.length-1&&(this.finalKeyframe=d)}}if(this.resolveNoneKeyframes(),!J.has(c)||2!==a.length)return;let[d,e]=a,f=X(d),g=X(e);if(f!==g)if(aa(f)&&aa(g))for(let b=0;b<a.length;b++){let c=a[b];"string"==typeof c&&(a[b]=parseFloat(c))}else ad[c]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){let{unresolvedKeyframes:a,name:b}=this,c=[];for(let b=0;b<a.length;b++){var d;(null===a[b]||("number"==typeof(d=a[b])?0===d:null===d||"none"===d||"0"===d||au(d)))&&c.push(b)}c.length&&function(a,b,c){let d,e=0;for(;e<a.length&&!d;){let b=a[e];"string"==typeof b&&!aX.has(b)&&aJ(b).values.length&&(d=a[e]),e++}if(d&&c)for(let e of b)a[e]=aW(c,d)}(a,c,b)}measureInitialState(){let{element:a,unresolvedKeyframes:b,name:c}=this;if(!a||!a.current)return;"height"===c&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=ad[c](a.measureViewportBox(),window.getComputedStyle(a.current)),b[0]=this.measuredOrigin;let d=b[b.length-1];void 0!==d&&a.getValue(c,d).jump(d,!1)}measureEndState(){let{element:a,name:b,unresolvedKeyframes:c}=this;if(!a||!a.current)return;let d=a.getValue(b);d&&d.jump(this.measuredOrigin,!1);let e=c.length-1,f=c[e];c[e]=ad[b](a.measureViewportBox(),window.getComputedStyle(a.current)),null!==f&&void 0===this.finalKeyframe&&(this.finalKeyframe=f),this.removedTransforms?.length&&this.removedTransforms.forEach(([b,c])=>{a.getValue(b).set(c)}),this.resolveNoneKeyframes()}}let aZ=a=>!!(a&&a.getVelocity);function a$(){d=void 0}let a_={now:()=>(void 0===d&&a_.set(al.isProcessing||af.useManualTiming?al.timestamp:performance.now()),d),set:a=>{d=a,queueMicrotask(a$)}};function a0(a,b){-1===a.indexOf(b)&&a.push(b)}function a1(a,b){let c=a.indexOf(b);c>-1&&a.splice(c,1)}class a2{constructor(){this.subscriptions=[]}add(a){return a0(this.subscriptions,a),()=>a1(this.subscriptions,a)}notify(a,b,c){let d=this.subscriptions.length;if(d)if(1===d)this.subscriptions[0](a,b,c);else for(let e=0;e<d;e++){let d=this.subscriptions[e];d&&d(a,b,c)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}let a3={current:void 0};class a4{constructor(a,b={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(a,b=!0)=>{let c=a_.now();if(this.updatedAt!==c&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(a),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(let a of this.dependents)a.dirty();b&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(a),this.owner=b.owner}setCurrent(a){this.current=a,this.updatedAt=a_.now(),null===this.canTrackVelocity&&void 0!==a&&(this.canTrackVelocity=!isNaN(parseFloat(this.current)))}setPrevFrameValue(a=this.current){this.prevFrameValue=a,this.prevUpdatedAt=this.updatedAt}onChange(a){return this.on("change",a)}on(a,b){this.events[a]||(this.events[a]=new a2);let c=this.events[a].add(b);return"change"===a?()=>{c(),aj.read(()=>{this.events.change.getSize()||this.stop()})}:c}clearListeners(){for(let a in this.events)this.events[a].clear()}attach(a,b){this.passiveEffect=a,this.stopPassiveEffect=b}set(a,b=!0){b&&this.passiveEffect?this.passiveEffect(a,this.updateAndNotify):this.updateAndNotify(a,b)}setWithVelocity(a,b,c){this.set(b),this.prev=void 0,this.prevFrameValue=a,this.prevUpdatedAt=this.updatedAt-c}jump(a,b=!0){this.updateAndNotify(a),this.prev=a,this.prevUpdatedAt=this.prevFrameValue=void 0,b&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(a){this.dependents||(this.dependents=new Set),this.dependents.add(a)}removeDependent(a){this.dependents&&this.dependents.delete(a)}get(){return a3.current&&a3.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var a;let b=a_.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||b-this.updatedAt>30)return 0;let c=Math.min(this.updatedAt-this.prevUpdatedAt,30);return a=parseFloat(this.current)-parseFloat(this.prevFrameValue),c?1e3/c*a:0}start(a){return this.stop(),new Promise(b=>{this.hasAnimated=!0,this.animation=a(b),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function a5(a,b){return new a4(a,b)}let a6=[...W,aE,aN],{schedule:a7}=ai(queueMicrotask,!1),a8={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},a9={};for(let a in a8)a9[a]={isEnabled:b=>a8[a].some(a=>!!b[a])};let ba=()=>({translate:0,scale:1,origin:0,originPoint:0}),bb=()=>({x:ba(),y:ba()}),bc=()=>({min:0,max:0}),bd=()=>({x:bc(),y:bc()}),be="undefined"!=typeof window,bf={current:null},bg={current:!1},bh=new WeakMap;function bi(a){return null!==a&&"object"==typeof a&&"function"==typeof a.start}function bj(a){return"string"==typeof a||Array.isArray(a)}let bk=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],bl=["initial",...bk];function bm(a){return bi(a.animate)||bl.some(b=>bj(a[b]))}function bn(a){return!!(bm(a)||a.variants)}function bo(a){let b=[{},{}];return a?.values.forEach((a,c)=>{b[0][c]=a.get(),b[1][c]=a.getVelocity()}),b}function bp(a,b,c,d){if("function"==typeof b){let[e,f]=bo(d);b=b(void 0!==c?c:a.custom,e,f)}if("string"==typeof b&&(b=a.variants&&a.variants[b]),"function"==typeof b){let[e,f]=bo(d);b=b(void 0!==c?c:a.custom,e,f)}return b}let bq=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class br{scrapeMotionValuesFromProps(a,b,c){return{}}constructor({parent:a,props:b,presenceContext:c,reducedMotionConfig:d,blockInitialAnimation:e,visualState:f},g={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=at,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let a=a_.now();this.renderScheduledAt<a&&(this.renderScheduledAt=a,aj.render(this.render,!1,!0))};let{latestValues:h,renderState:i}=f;this.latestValues=h,this.baseTarget={...h},this.initialValues=b.initial?{...h}:{},this.renderState=i,this.parent=a,this.props=b,this.presenceContext=c,this.depth=a?a.depth+1:0,this.reducedMotionConfig=d,this.options=g,this.blockInitialAnimation=!!e,this.isControllingVariants=bm(b),this.isVariantNode=bn(b),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(a&&a.current);let{willChange:j,...k}=this.scrapeMotionValuesFromProps(b,{},this);for(let a in k){let b=k[a];void 0!==h[a]&&aZ(b)&&b.set(h[a],!1)}}mount(a){this.current=a,bh.set(a,this),this.projection&&!this.projection.instance&&this.projection.mount(a),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((a,b)=>this.bindToMotionValue(b,a)),bg.current||function(){if(bg.current=!0,be)if(window.matchMedia){let a=window.matchMedia("(prefers-reduced-motion)"),b=()=>bf.current=a.matches;a.addEventListener("change",b),b()}else bf.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||bf.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let a in this.projection&&this.projection.unmount(),ak(this.notifyUpdate),ak(this.render),this.valueSubscriptions.forEach(a=>a()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[a].clear();for(let a in this.features){let b=this.features[a];b&&(b.unmount(),b.isMounted=!1)}this.current=null}bindToMotionValue(a,b){let c;this.valueSubscriptions.has(a)&&this.valueSubscriptions.get(a)();let d=g.has(a);d&&this.onBindTransform&&this.onBindTransform();let e=b.on("change",b=>{this.latestValues[a]=b,this.props.onUpdate&&aj.preRender(this.notifyUpdate),d&&this.projection&&(this.projection.isTransformDirty=!0)}),f=b.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(c=window.MotionCheckAppearSync(this,a,b)),this.valueSubscriptions.set(a,()=>{e(),f(),c&&c(),b.owner&&b.stop()})}sortNodePosition(a){return this.current&&this.sortInstanceNodePosition&&this.type===a.type?this.sortInstanceNodePosition(this.current,a.current):0}updateFeatures(){let a="animation";for(a in a9){let b=a9[a];if(!b)continue;let{isEnabled:c,Feature:d}=b;if(!this.features[a]&&d&&c(this.props)&&(this.features[a]=new d(this)),this.features[a]){let b=this.features[a];b.isMounted?b.update():(b.mount(),b.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):bd()}getStaticValue(a){return this.latestValues[a]}setStaticValue(a,b){this.latestValues[a]=b}update(a,b){(a.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=a,this.prevPresenceContext=this.presenceContext,this.presenceContext=b;for(let b=0;b<bq.length;b++){let c=bq[b];this.propEventSubscriptions[c]&&(this.propEventSubscriptions[c](),delete this.propEventSubscriptions[c]);let d=a["on"+c];d&&(this.propEventSubscriptions[c]=this.on(c,d))}this.prevMotionValues=function(a,b,c){for(let d in b){let e=b[d],f=c[d];if(aZ(e))a.addValue(d,e);else if(aZ(f))a.addValue(d,a5(e,{owner:a}));else if(f!==e)if(a.hasValue(d)){let b=a.getValue(d);!0===b.liveStyle?b.jump(e):b.hasAnimated||b.set(e)}else{let b=a.getStaticValue(d);a.addValue(d,a5(void 0!==b?b:e,{owner:a}))}}for(let d in c)void 0===b[d]&&a.removeValue(d);return b}(this,this.scrapeMotionValuesFromProps(a,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(a){return this.props.variants?this.props.variants[a]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(a){let b=this.getClosestVariantNode();if(b)return b.variantChildren&&b.variantChildren.add(a),()=>b.variantChildren.delete(a)}addValue(a,b){let c=this.values.get(a);b!==c&&(c&&this.removeValue(a),this.bindToMotionValue(a,b),this.values.set(a,b),this.latestValues[a]=b.get())}removeValue(a){this.values.delete(a);let b=this.valueSubscriptions.get(a);b&&(b(),this.valueSubscriptions.delete(a)),delete this.latestValues[a],this.removeValueFromRenderState(a,this.renderState)}hasValue(a){return this.values.has(a)}getValue(a,b){if(this.props.values&&this.props.values[a])return this.props.values[a];let c=this.values.get(a);return void 0===c&&void 0!==b&&(c=a5(null===b?void 0:b,{owner:this}),this.addValue(a,c)),c}readValue(a,b){let c=void 0===this.latestValues[a]&&this.current?this.getBaseTargetFromProps(this.props,a)??this.readValueFromInstance(this.current,a,this.options):this.latestValues[a];if(null!=c){if("string"==typeof c&&($(c)||au(c)))c=parseFloat(c);else{let d;d=c,!a6.find(V(d))&&aN.test(b)&&(c=aW(a,b))}this.setBaseTarget(a,aZ(c)?c.get():c)}return aZ(c)?c.get():c}setBaseTarget(a,b){this.baseTarget[a]=b}getBaseTarget(a){let b,{initial:c}=this.props;if("string"==typeof c||"object"==typeof c){let d=bp(this.props,c,this.presenceContext?.custom);d&&(b=d[a])}if(c&&void 0!==b)return b;let d=this.getBaseTargetFromProps(this.props,a);return void 0===d||aZ(d)?void 0!==this.initialValues[a]&&void 0===b?void 0:this.baseTarget[a]:d}on(a,b){return this.events[a]||(this.events[a]=new a2),this.events[a].add(b)}notify(a,...b){this.events[a]&&this.events[a].notify(...b)}scheduleRenderMicrotask(){a7.render(this.render)}}class bs extends br{constructor(){super(...arguments),this.KeyframeResolver=aY}sortInstanceNodePosition(a,b){return 2&a.compareDocumentPosition(b)?1:-1}getBaseTargetFromProps(a,b){return a.style?a.style[b]:void 0}removeValueFromRenderState(a,{vars:b,style:c}){delete b[a],delete c[a]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:a}=this.props;aZ(a)&&(this.childSubscription=a.on("change",a=>{this.current&&(this.current.textContent=`${a}`)}))}}let bt=(a,b)=>b&&"number"==typeof a?b.transform(a):a,bu={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},bv=f.length;function bw(a,b,c){let{style:d,vars:e,transformOrigin:h}=a,i=!1,j=!1;for(let a in b){let c=b[a];if(g.has(a)){i=!0;continue}if(s(a)){e[a]=c;continue}{let b=bt(c,aT[a]);a.startsWith("origin")?(j=!0,h[a]=b):d[a]=b}}if(!b.transform&&(i||c?d.transform=function(a,b,c){let d="",e=!0;for(let g=0;g<bv;g++){let h=f[g],i=a[h];if(void 0===i)continue;let j=!0;if(!(j="number"==typeof i?i===+!!h.startsWith("scale"):0===parseFloat(i))||c){let a=bt(i,aT[h]);if(!j){e=!1;let b=bu[h]||h;d+=`${b}(${a}) `}c&&(b[h]=a)}}return d=d.trim(),c?d=c(b,e?"":d):e&&(d="none"),d}(b,a.transform,c):d.transform&&(d.transform="none")),j){let{originX:a="50%",originY:b="50%",originZ:c=0}=h;d.transformOrigin=`${a} ${b} ${c}`}}function bx(a,{style:b,vars:c},d,e){let f,g=a.style;for(f in b)g[f]=b[f];for(f in e?.applyProjectionStyles(g,d),c)g.setProperty(f,c[f])}let by={};function bz(a,{layout:b,layoutId:c}){return g.has(a)||a.startsWith("origin")||(b||void 0!==c)&&(!!by[a]||"opacity"===a)}function bA(a,b,c){let{style:d}=a,e={};for(let f in d)(aZ(d[f])||b.style&&aZ(b.style[f])||bz(f,a)||c?.getValue(f)?.liveStyle!==void 0)&&(e[f]=d[f]);return e}class bB extends bs{constructor(){super(...arguments),this.type="html",this.renderInstance=bx}readValueFromInstance(a,b){if(g.has(b))return this.projection?.isProjecting?o(b):((a,b)=>{let{transform:c="none"}=getComputedStyle(a);return p(c,b)})(a,b);{let c=window.getComputedStyle(a),d=(s(b)?c.getPropertyValue(b):c[b])||0;return"string"==typeof d?d.trim():d}}measureInstanceViewportBox(a,{transformPagePoint:b}){return I(a,b)}build(a,b,c){bw(a,b,c.transformTemplate)}scrapeMotionValuesFromProps(a,b,c){return bA(a,b,c)}}let bC=a=>a.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),bD={offset:"stroke-dashoffset",array:"stroke-dasharray"},bE={offset:"strokeDashoffset",array:"strokeDasharray"};function bF(a,{attrX:b,attrY:c,attrScale:d,pathLength:e,pathSpacing:f=1,pathOffset:g=0,...h},i,j,k){if(bw(a,h,j),i){a.style.viewBox&&(a.attrs.viewBox=a.style.viewBox);return}a.attrs=a.style,a.style={};let{attrs:l,style:m}=a;l.transform&&(m.transform=l.transform,delete l.transform),(m.transform||l.transformOrigin)&&(m.transformOrigin=l.transformOrigin??"50% 50%",delete l.transformOrigin),m.transform&&(m.transformBox=k?.transformBox??"fill-box",delete l.transformBox),void 0!==b&&(l.x=b),void 0!==c&&(l.y=c),void 0!==d&&(l.scale=d),void 0!==e&&function(a,b,c=1,d=0,e=!0){a.pathLength=1;let f=e?bD:bE;a[f.offset]=R.transform(-d);let g=R.transform(b),h=R.transform(c);a[f.array]=`${g} ${h}`}(l,e,f,g,!1)}let bG=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]),bH=a=>"string"==typeof a&&"svg"===a.toLowerCase();function bI(a,b,c){let d=bA(a,b,c);for(let c in a)(aZ(a[c])||aZ(b[c]))&&(d[-1!==f.indexOf(c)?"attr"+c.charAt(0).toUpperCase()+c.substring(1):c]=a[c]);return d}class bJ extends bs{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=bd}getBaseTargetFromProps(a,b){return a[b]}readValueFromInstance(a,b){if(g.has(b)){let a=aV(b);return a&&a.default||0}return b=bG.has(b)?b:bC(b),a.getAttribute(b)}scrapeMotionValuesFromProps(a,b,c){return bI(a,b,c)}build(a,b,c){bF(a,b,this.isSVGTag,c.transformTemplate,c.style)}renderInstance(a,b,c,d){for(let c in bx(a,b,void 0,d),b.attrs)a.setAttribute(bG.has(c)?c:bC(c),b.attrs[c])}mount(a){this.isSVGTag=bH(a.tagName),super.mount(a)}}let bK=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function bL(a){if("string"!=typeof a||a.includes("-"));else if(bK.indexOf(a)>-1||/[A-Z]/u.test(a))return!0;return!1}var bM=c(687);let bN=(0,e.createContext)({}),bO=(0,e.createContext)({strict:!1}),bP=(0,e.createContext)({transformPagePoint:a=>a,isStatic:!1,reducedMotion:"never"}),bQ=(0,e.createContext)({});function bR(a){return Array.isArray(a)?a.join(" "):a}let bS=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function bT(a,b,c){for(let d in b)aZ(b[d])||bz(d,c)||(a[d]=b[d])}let bU=()=>({...bS(),attrs:{}}),bV=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function bW(a){return a.startsWith("while")||a.startsWith("drag")&&"draggable"!==a||a.startsWith("layout")||a.startsWith("onTap")||a.startsWith("onPan")||a.startsWith("onLayout")||bV.has(a)}let bX=a=>!bW(a);try{!function(a){"function"==typeof a&&(bX=b=>b.startsWith("on")?!bW(b):a(b))}(require("@emotion/is-prop-valid").default)}catch{}let bY=(0,e.createContext)(null);function bZ(a){return aZ(a)?a.get():a}let b$=a=>(b,c)=>{let d=(0,e.useContext)(bQ),f=(0,e.useContext)(bY),g=()=>(function({scrapeMotionValuesFromProps:a,createRenderState:b},c,d,e){return{latestValues:function(a,b,c,d){let e={},f=d(a,{});for(let a in f)e[a]=bZ(f[a]);let{initial:g,animate:h}=a,i=bm(a),j=bn(a);b&&j&&!i&&!1!==a.inherit&&(void 0===g&&(g=b.initial),void 0===h&&(h=b.animate));let k=!!c&&!1===c.initial,l=(k=k||!1===g)?h:g;if(l&&"boolean"!=typeof l&&!bi(l)){let b=Array.isArray(l)?l:[l];for(let c=0;c<b.length;c++){let d=bp(a,b[c]);if(d){let{transitionEnd:a,transition:b,...c}=d;for(let a in c){let b=c[a];if(Array.isArray(b)){let a=k?b.length-1:0;b=b[a]}null!==b&&(e[a]=b)}for(let b in a)e[b]=a[b]}}}return e}(c,d,e,a),renderState:b()}})(a,b,d,f);return c?g():function(a){let b=(0,e.useRef)(null);return null===b.current&&(b.current=a()),b.current}(g)},b_=b$({scrapeMotionValuesFromProps:bA,createRenderState:bS}),b0=b$({scrapeMotionValuesFromProps:bI,createRenderState:bU}),b1=Symbol.for("motionComponentSymbol");function b2(a){return a&&"object"==typeof a&&Object.prototype.hasOwnProperty.call(a,"current")}let b3="data-"+bC("framerAppearId"),b4=(0,e.createContext)({}),b5=be?e.useLayoutEffect:e.useEffect;function b6(a,{forwardMotionProps:b=!1}={},c,d){c&&function(a){for(let b in a)a9[b]={...a9[b],...a[b]}}(c);let f=bL(a)?b0:b_;function g(c,g){var h;let i,j={...(0,e.useContext)(bP),...c,layoutId:function({layoutId:a}){let b=(0,e.useContext)(bN).id;return b&&void 0!==a?b+"-"+a:a}(c)},{isStatic:k}=j,l=function(a){let{initial:b,animate:c}=function(a,b){if(bm(a)){let{initial:b,animate:c}=a;return{initial:!1===b||bj(b)?b:void 0,animate:bj(c)?c:void 0}}return!1!==a.inherit?b:{}}(a,(0,e.useContext)(bQ));return(0,e.useMemo)(()=>({initial:b,animate:c}),[bR(b),bR(c)])}(c),m=f(c,k);if(!k&&be){(0,e.useContext)(bO).strict;let b=function(a){let{drag:b,layout:c}=a9;if(!b&&!c)return{};let d={...b,...c};return{MeasureLayout:b?.isEnabled(a)||c?.isEnabled(a)?d.MeasureLayout:void 0,ProjectionNode:d.ProjectionNode}}(j);i=b.MeasureLayout,l.visualElement=function(a,b,c,d,f){let{visualElement:g}=(0,e.useContext)(bQ),h=(0,e.useContext)(bO),i=(0,e.useContext)(bY),j=(0,e.useContext)(bP).reducedMotion,k=(0,e.useRef)(null);d=d||h.renderer,!k.current&&d&&(k.current=d(a,{visualState:b,parent:g,props:c,presenceContext:i,blockInitialAnimation:!!i&&!1===i.initial,reducedMotionConfig:j}));let l=k.current,m=(0,e.useContext)(b4);l&&!l.projection&&f&&("html"===l.type||"svg"===l.type)&&function(a,b,c,d){let{layoutId:e,layout:f,drag:g,dragConstraints:h,layoutScroll:i,layoutRoot:j,layoutCrossfade:k}=b;a.projection=new c(a.latestValues,b["data-framer-portal-id"]?void 0:function a(b){if(b)return!1!==b.options.allowProjection?b.projection:a(b.parent)}(a.parent)),a.projection.setOptions({layoutId:e,layout:f,alwaysMeasureLayout:!!g||h&&b2(h),visualElement:a,animationType:"string"==typeof f?f:"both",initialPromotionConfig:d,crossfade:k,layoutScroll:i,layoutRoot:j})}(k.current,c,f,m);let n=(0,e.useRef)(!1);(0,e.useInsertionEffect)(()=>{l&&n.current&&l.update(c,i)});let o=c[b3],p=(0,e.useRef)(!!o&&!window.MotionHandoffIsComplete?.(o)&&window.MotionHasOptimisedAnimation?.(o));return b5(()=>{l&&(n.current=!0,window.MotionIsMounted=!0,l.updateFeatures(),l.scheduleRenderMicrotask(),p.current&&l.animationState&&l.animationState.animateChanges())}),(0,e.useEffect)(()=>{l&&(!p.current&&l.animationState&&l.animationState.animateChanges(),p.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(o)}),p.current=!1))}),l}(a,m,j,d,b.ProjectionNode)}return(0,bM.jsxs)(bQ.Provider,{value:l,children:[i&&l.visualElement?(0,bM.jsx)(i,{visualElement:l.visualElement,...j}):null,function(a,b,c,{latestValues:d},f,g=!1){let h=(bL(a)?function(a,b,c,d){let f=(0,e.useMemo)(()=>{let c=bU();return bF(c,b,bH(d),a.transformTemplate,a.style),{...c.attrs,style:{...c.style}}},[b]);if(a.style){let b={};bT(b,a.style,a),f.style={...b,...f.style}}return f}:function(a,b){let c={},d=function(a,b){let c=a.style||{},d={};return bT(d,c,a),Object.assign(d,function({transformTemplate:a},b){return(0,e.useMemo)(()=>{let c=bS();return bw(c,b,a),Object.assign({},c.vars,c.style)},[b])}(a,b)),d}(a,b);return a.drag&&!1!==a.dragListener&&(c.draggable=!1,d.userSelect=d.WebkitUserSelect=d.WebkitTouchCallout="none",d.touchAction=!0===a.drag?"none":`pan-${"x"===a.drag?"y":"x"}`),void 0===a.tabIndex&&(a.onTap||a.onTapStart||a.whileTap)&&(c.tabIndex=0),c.style=d,c})(b,d,f,a),i=function(a,b,c){let d={};for(let e in a)("values"!==e||"object"!=typeof a.values)&&(bX(e)||!0===c&&bW(e)||!b&&!bW(e)||a.draggable&&e.startsWith("onDrag"))&&(d[e]=a[e]);return d}(b,"string"==typeof a,g),j=a!==e.Fragment?{...i,...h,ref:c}:{},{children:k}=b,l=(0,e.useMemo)(()=>aZ(k)?k.get():k,[k]);return(0,e.createElement)(a,{...j,children:l})}(a,c,(h=l.visualElement,(0,e.useCallback)(a=>{a&&m.onMount&&m.onMount(a),h&&(a?h.mount(a):h.unmount()),g&&("function"==typeof g?g(a):b2(g)&&(g.current=a))},[h])),m,k,b)]})}g.displayName=`motion.${"string"==typeof a?a:`create(${a.displayName??a.name??""})`}`;let h=(0,e.forwardRef)(g);return h[b1]=a,h}function b7(a,b,c){let d=a.getProps();return bp(d,b,void 0!==c?c:d.custom,a)}function b8(a,b){return a?.[b]??a?.default??a}let b9=a=>Array.isArray(a);function ca(a,b){let c=a.getValue("willChange");if(aZ(c)&&c.add)return c.add(b);if(!c&&af.WillChange){let c=new af.WillChange("auto");a.addValue("willChange",c),c.add(b)}}let cb=(a,b)=>c=>b(a(c)),cc=(...a)=>a.reduce(cb),cd=a=>1e3*a,ce={layout:0,mainThread:0,waapi:0};function cf(a,b,c){return(c<0&&(c+=1),c>1&&(c-=1),c<1/6)?a+(b-a)*6*c:c<.5?b:c<2/3?a+(b-a)*(2/3-c)*6:a}function cg(a,b){return c=>c>0?b:a}let ch=(a,b,c)=>{let d=a*a,e=c*(b*b-d)+d;return e<0?0:Math.sqrt(e)},ci=[aC,aB,aD];function cj(a){let b=ci.find(b=>b.test(a));if(Y(!!b,`'${a}' is not an animatable color. Use the equivalent color code instead.`,"color-not-animatable"),!b)return!1;let c=b.parse(a);return b===aD&&(c=function({hue:a,saturation:b,lightness:c,alpha:d}){a/=360,c/=100;let e=0,f=0,g=0;if(b/=100){let d=c<.5?c*(1+b):c+b-c*b,h=2*c-d;e=cf(h,d,a+1/3),f=cf(h,d,a),g=cf(h,d,a-1/3)}else e=f=g=c;return{red:Math.round(255*e),green:Math.round(255*f),blue:Math.round(255*g),alpha:d}}(c)),c}let ck=(a,b)=>{let c=cj(a),d=cj(b);if(!c||!d)return cg(a,b);let e={...c};return a=>(e.red=ch(c.red,d.red,a),e.green=ch(c.green,d.green,a),e.blue=ch(c.blue,d.blue,a),e.alpha=x(c.alpha,d.alpha,a),aB.transform(e))},cl=new Set(["none","hidden"]);function cm(a,b){return c=>x(a,b,c)}function cn(a){return"number"==typeof a?cm:"string"==typeof a?u(a)?cg:aE.test(a)?ck:cq:Array.isArray(a)?co:"object"==typeof a?aE.test(a)?ck:cp:cg}function co(a,b){let c=[...a],d=c.length,e=a.map((a,c)=>cn(a)(a,b[c]));return a=>{for(let b=0;b<d;b++)c[b]=e[b](a);return c}}function cp(a,b){let c={...a,...b},d={};for(let e in c)void 0!==a[e]&&void 0!==b[e]&&(d[e]=cn(a[e])(a[e],b[e]));return a=>{for(let b in d)c[b]=d[b](a);return c}}let cq=(a,b)=>{let c=aN.createTransformer(b),d=aJ(a),e=aJ(b);return d.indexes.var.length===e.indexes.var.length&&d.indexes.color.length===e.indexes.color.length&&d.indexes.number.length>=e.indexes.number.length?cl.has(a)&&!e.values.length||cl.has(b)&&!d.values.length?function(a,b){return cl.has(a)?c=>c<=0?a:b:c=>c>=1?b:a}(a,b):cc(co(function(a,b){let c=[],d={color:0,var:0,number:0};for(let e=0;e<b.values.length;e++){let f=b.types[e],g=a.indexes[f][d[f]],h=a.values[g]??0;c[e]=h,d[f]++}return c}(d,e),e.values),c):(Y(!0,`Complex values '${a}' and '${b}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`,"complex-values-different"),cg(a,b))};function cr(a,b,c){return"number"==typeof a&&"number"==typeof b&&"number"==typeof c?x(a,b,c):cn(a)(a,b)}let cs=a=>{let b=({timestamp:b})=>a(b);return{start:(a=!0)=>aj.update(b,a),stop:()=>ak(b),now:()=>al.isProcessing?al.timestamp:a_.now()}},ct=(a,b,c=10)=>{let d="",e=Math.max(Math.round(b/c),2);for(let b=0;b<e;b++)d+=Math.round(1e4*a(b/(e-1)))/1e4+", ";return`linear(${d.substring(0,d.length-2)})`};function cu(a){let b=0,c=a.next(b);for(;!c.done&&b<2e4;)b+=50,c=a.next(b);return b>=2e4?1/0:b}function cv(a,b,c){var d,e;let f=Math.max(b-5,0);return d=c-a(f),(e=b-f)?1e3/e*d:0}let cw={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function cx(a,b){return a*Math.sqrt(1-b*b)}let cy=["duration","bounce"],cz=["stiffness","damping","mass"];function cA(a,b){return b.some(b=>void 0!==a[b])}function cB(a=cw.visualDuration,b=cw.bounce){let c,d="object"!=typeof a?{visualDuration:a,keyframes:[0,1],bounce:b}:a,{restSpeed:e,restDelta:f}=d,g=d.keyframes[0],h=d.keyframes[d.keyframes.length-1],i={done:!1,value:g},{stiffness:j,damping:k,mass:l,duration:m,velocity:n,isResolvedFromDuration:o}=function(a){let b={velocity:cw.velocity,stiffness:cw.stiffness,damping:cw.damping,mass:cw.mass,isResolvedFromDuration:!1,...a};if(!cA(a,cz)&&cA(a,cy))if(a.visualDuration){let c=2*Math.PI/(1.2*a.visualDuration),d=c*c,e=2*K(.05,1,1-(a.bounce||0))*Math.sqrt(d);b={...b,mass:cw.mass,stiffness:d,damping:e}}else{let c=function({duration:a=cw.duration,bounce:b=cw.bounce,velocity:c=cw.velocity,mass:d=cw.mass}){let e,f;Y(a<=cd(cw.maxDuration),"Spring duration must be 10 seconds or less","spring-duration-limit");let g=1-b;g=K(cw.minDamping,cw.maxDamping,g),a=K(cw.minDuration,cw.maxDuration,a/1e3),g<1?(e=b=>{let d=b*g,e=d*a;return .001-(d-c)/cx(b,g)*Math.exp(-e)},f=b=>{let d=b*g*a,f=Math.pow(g,2)*Math.pow(b,2)*a,h=Math.exp(-d),i=cx(Math.pow(b,2),g);return(d*c+c-f)*h*(-e(b)+.001>0?-1:1)/i}):(e=b=>-.001+Math.exp(-b*a)*((b-c)*a+1),f=b=>a*a*(c-b)*Math.exp(-b*a));let h=function(a,b,c){let d=c;for(let c=1;c<12;c++)d-=a(d)/b(d);return d}(e,f,5/a);if(a=cd(a),isNaN(h))return{stiffness:cw.stiffness,damping:cw.damping,duration:a};{let b=Math.pow(h,2)*d;return{stiffness:b,damping:2*g*Math.sqrt(d*b),duration:a}}}(a);(b={...b,...c,mass:cw.mass}).isResolvedFromDuration=!0}return b}({...d,velocity:-((d.velocity||0)/1e3)}),p=n||0,q=k/(2*Math.sqrt(j*l)),r=h-g,s=Math.sqrt(j/l)/1e3,t=5>Math.abs(r);if(e||(e=t?cw.restSpeed.granular:cw.restSpeed.default),f||(f=t?cw.restDelta.granular:cw.restDelta.default),q<1){let a=cx(s,q);c=b=>h-Math.exp(-q*s*b)*((p+q*s*r)/a*Math.sin(a*b)+r*Math.cos(a*b))}else if(1===q)c=a=>h-Math.exp(-s*a)*(r+(p+s*r)*a);else{let a=s*Math.sqrt(q*q-1);c=b=>{let c=Math.exp(-q*s*b),d=Math.min(a*b,300);return h-c*((p+q*s*r)*Math.sinh(d)+a*r*Math.cosh(d))/a}}let u={calculatedDuration:o&&m||null,next:a=>{let b=c(a);if(o)i.done=a>=m;else{let d=0===a?p:0;q<1&&(d=0===a?cd(p):cv(c,a,b));let g=Math.abs(h-b)<=f;i.done=Math.abs(d)<=e&&g}return i.value=i.done?h:b,i},toString:()=>{let a=Math.min(cu(u),2e4),b=ct(b=>u.next(a*b).value,a,30);return a+"ms "+b},toTransition:()=>{}};return u}function cC({keyframes:a,velocity:b=0,power:c=.8,timeConstant:d=325,bounceDamping:e=10,bounceStiffness:f=500,modifyTarget:g,min:h,max:i,restDelta:j=.5,restSpeed:k}){let l,m,n=a[0],o={done:!1,value:n},p=c*b,q=n+p,r=void 0===g?q:g(q);r!==q&&(p=r-n);let s=a=>-p*Math.exp(-a/d),t=a=>r+s(a),u=a=>{let b=s(a),c=t(a);o.done=Math.abs(b)<=j,o.value=o.done?r:c},v=a=>{let b;if(b=o.value,void 0!==h&&b<h||void 0!==i&&b>i){var c;l=a,m=cB({keyframes:[o.value,(c=o.value,void 0===h?i:void 0===i||Math.abs(h-c)<Math.abs(i-c)?h:i)],velocity:cv(t,a,o.value),damping:e,stiffness:f,restDelta:j,restSpeed:k})}};return v(0),{calculatedDuration:null,next:a=>{let b=!1;return(m||void 0!==l||(b=!0,u(a),v(a)),void 0!==l&&a>=l)?m.next(a-l):(b||u(a),o)}}}cB.applyToOptions=a=>{let b=function(a,b=100,c){let d=c({...a,keyframes:[0,b]}),e=Math.min(cu(d),2e4);return{type:"keyframes",ease:a=>d.next(e*a).value/b,duration:e/1e3}}(a,100,cB);return a.ease=b.ease,a.duration=cd(b.duration),a.type="keyframes",a};let cD=(a,b,c)=>(((1-3*c+3*b)*a+(3*c-6*b))*a+3*b)*a;function cE(a,b,c,d){return a===b&&c===d?ae:e=>0===e||1===e?e:cD(function(a,b,c,d,e){let f,g,h=0;do(f=cD(g=b+(c-b)/2,d,e)-a)>0?c=g:b=g;while(Math.abs(f)>1e-7&&++h<12);return g}(e,0,1,a,c),b,d)}let cF=cE(.42,0,1,1),cG=cE(0,0,.58,1),cH=cE(.42,0,.58,1),cI=a=>b=>b<=.5?a(2*b)/2:(2-a(2*(1-b)))/2,cJ=a=>b=>1-a(1-b),cK=cE(.33,1.53,.69,.99),cL=cJ(cK),cM=cI(cL),cN=a=>(a*=2)<1?.5*cL(a):.5*(2-Math.pow(2,-10*(a-1))),cO=a=>1-Math.sin(Math.acos(a)),cP=cJ(cO),cQ=cI(cO),cR=a=>Array.isArray(a)&&"number"==typeof a[0],cS={linear:ae,easeIn:cF,easeInOut:cH,easeOut:cG,circIn:cO,circInOut:cQ,circOut:cP,backIn:cL,backInOut:cM,backOut:cK,anticipate:cN},cT=a=>{if(cR(a)){Z(4===a.length,"Cubic bezier arrays must contain four numerical values.","cubic-bezier-length");let[b,c,d,e]=a;return cE(b,c,d,e)}return"string"==typeof a?(Z(void 0!==cS[a],`Invalid easing type '${a}'`,"invalid-easing-type"),cS[a]):a},cU=(a,b,c)=>{let d=b-a;return 0===d?1:(c-a)/d};function cV({duration:a=300,keyframes:b,times:c,ease:d="easeInOut"}){var e;let f=Array.isArray(d)&&"number"!=typeof d[0]?d.map(cT):cT(d),g={done:!1,value:b[0]},h=function(a,b,{clamp:c=!0,ease:d,mixer:e}={}){let f=a.length;if(Z(f===b.length,"Both input and output ranges must be the same length","range-length"),1===f)return()=>b[0];if(2===f&&b[0]===b[1])return()=>b[1];let g=a[0]===a[1];a[0]>a[f-1]&&(a=[...a].reverse(),b=[...b].reverse());let h=function(a,b,c){let d=[],e=c||af.mix||cr,f=a.length-1;for(let c=0;c<f;c++){let f=e(a[c],a[c+1]);b&&(f=cc(Array.isArray(b)?b[c]||ae:b,f)),d.push(f)}return d}(b,d,e),i=h.length,j=c=>{if(g&&c<a[0])return b[0];let d=0;if(i>1)for(;d<a.length-2&&!(c<a[d+1]);d++);let e=cU(a[d],a[d+1],c);return h[d](e)};return c?b=>j(K(a[0],a[f-1],b)):j}((e=c&&c.length===b.length?c:function(a){let b=[0];return!function(a,b){let c=a[a.length-1];for(let d=1;d<=b;d++){let e=cU(0,b,d);a.push(x(c,1,e))}}(b,a.length-1),b}(b),e.map(b=>b*a)),b,{ease:Array.isArray(f)?f:b.map(()=>f||cH).splice(0,b.length-1)});return{calculatedDuration:a,next:b=>(g.value=h(b),g.done=b>=a,g)}}let cW=a=>null!==a;function cX(a,{repeat:b,repeatType:c="loop"},d,e=1){let f=a.filter(cW),g=e<0||b&&"loop"!==c&&b%2==1?0:f.length-1;return g&&void 0!==d?d:f[g]}let cY={decay:cC,inertia:cC,tween:cV,keyframes:cV,spring:cB};function cZ(a){"string"==typeof a.type&&(a.type=cY[a.type])}class c${constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(a=>{this.resolve=a})}notifyFinished(){this.resolve()}then(a,b){return this.finished.then(a,b)}}let c_=a=>a/100;class c0 extends c${constructor(a){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{let{motionValue:a}=this.options;a&&a.updatedAt!==a_.now()&&this.tick(a_.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},ce.mainThread++,this.options=a,this.initAnimation(),this.play(),!1===a.autoplay&&this.pause()}initAnimation(){let{options:a}=this;cZ(a);let{type:b=cV,repeat:c=0,repeatDelay:d=0,repeatType:e,velocity:f=0}=a,{keyframes:g}=a,h=b||cV;h!==cV&&"number"!=typeof g[0]&&(this.mixKeyframes=cc(c_,cr(g[0],g[1])),g=[0,100]);let i=h({...a,keyframes:g});"mirror"===e&&(this.mirroredGenerator=h({...a,keyframes:[...g].reverse(),velocity:-f})),null===i.calculatedDuration&&(i.calculatedDuration=cu(i));let{calculatedDuration:j}=i;this.calculatedDuration=j,this.resolvedDuration=j+d,this.totalDuration=this.resolvedDuration*(c+1)-d,this.generator=i}updateTime(a){let b=Math.round(a-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=b}tick(a,b=!1){let{generator:c,totalDuration:d,mixKeyframes:e,mirroredGenerator:f,resolvedDuration:g,calculatedDuration:h}=this;if(null===this.startTime)return c.next(0);let{delay:i=0,keyframes:j,repeat:k,repeatType:l,repeatDelay:m,type:n,onUpdate:o,finalKeyframe:p}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,a):this.speed<0&&(this.startTime=Math.min(a-d/this.speed,this.startTime)),b?this.currentTime=a:this.updateTime(a);let q=this.currentTime-i*(this.playbackSpeed>=0?1:-1),r=this.playbackSpeed>=0?q<0:q>d;this.currentTime=Math.max(q,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=d);let s=this.currentTime,t=c;if(k){let a=Math.min(this.currentTime,d)/g,b=Math.floor(a),c=a%1;!c&&a>=1&&(c=1),1===c&&b--,(b=Math.min(b,k+1))%2&&("reverse"===l?(c=1-c,m&&(c-=m/g)):"mirror"===l&&(t=f)),s=K(0,1,c)*g}let u=r?{done:!1,value:j[0]}:t.next(s);e&&(u.value=e(u.value));let{done:v}=u;r||null===h||(v=this.playbackSpeed>=0?this.currentTime>=d:this.currentTime<=0);let w=null===this.holdTime&&("finished"===this.state||"running"===this.state&&v);return w&&n!==cC&&(u.value=cX(j,this.options,p,this.speed)),o&&o(u.value),w&&this.finish(),u}then(a,b){return this.finished.then(a,b)}get duration(){return this.calculatedDuration/1e3}get time(){return this.currentTime/1e3}set time(a){a=cd(a),this.currentTime=a,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=a:this.driver&&(this.startTime=this.driver.now()-a/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(a){this.updateTime(a_.now());let b=this.playbackSpeed!==a;this.playbackSpeed=a,b&&(this.time=this.currentTime/1e3)}play(){if(this.isStopped)return;let{driver:a=cs,startTime:b}=this.options;this.driver||(this.driver=a(a=>this.tick(a))),this.options.onPlay?.();let c=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=c):null!==this.holdTime?this.startTime=c-this.holdTime:this.startTime||(this.startTime=b??c),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(a_.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,ce.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(a){return this.startTime=0,this.tick(a,!0)}attachTimeline(a){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),a.observe(this)}}function c1(a){let b;return()=>(void 0===b&&(b=a()),b)}let c2=c1(()=>void 0!==window.ScrollTimeline),c3={},c4=function(a,b){let c=c1(a);return()=>c3[b]??c()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(a){return!1}return!0},"linearEasing"),c5=([a,b,c,d])=>`cubic-bezier(${a}, ${b}, ${c}, ${d})`,c6={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:c5([0,.65,.55,1]),circOut:c5([.55,0,1,.45]),backIn:c5([.31,.01,.66,-.59]),backOut:c5([.33,1.53,.69,.99])};function c7(a){return"function"==typeof a&&"applyToOptions"in a}class c8 extends c${constructor(a){if(super(),this.finishedTime=null,this.isStopped=!1,!a)return;let{element:b,name:c,keyframes:d,pseudoElement:e,allowFlatten:f=!1,finalKeyframe:g,onComplete:h}=a;this.isPseudoElement=!!e,this.allowFlatten=f,this.options=a,Z("string"!=typeof a.type,'Mini animate() doesn\'t support "type" as a string.',"mini-spring");let i=function({type:a,...b}){return c7(a)&&c4()?a.applyToOptions(b):(b.duration??(b.duration=300),b.ease??(b.ease="easeOut"),b)}(a);this.animation=function(a,b,c,{delay:d=0,duration:e=300,repeat:f=0,repeatType:g="loop",ease:h="easeOut",times:i}={},j){let k={[b]:c};i&&(k.offset=i);let l=function a(b,c){if(b)return"function"==typeof b?c4()?ct(b,c):"ease-out":cR(b)?c5(b):Array.isArray(b)?b.map(b=>a(b,c)||c6.easeOut):c6[b]}(h,e);Array.isArray(l)&&(k.easing=l),ah.value&&ce.waapi++;let m={delay:d,duration:e,easing:Array.isArray(l)?"linear":l,fill:"both",iterations:f+1,direction:"reverse"===g?"alternate":"normal"};j&&(m.pseudoElement=j);let n=a.animate(k,m);return ah.value&&n.finished.finally(()=>{ce.waapi--}),n}(b,c,d,i,e),!1===i.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!e){let a=cX(d,this.options,g,this.speed);this.updateMotionValue?this.updateMotionValue(a):function(a,b,c){b.startsWith("--")?a.style.setProperty(b,c):a.style[b]=c}(b,c,a),this.animation.cancel()}h?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(a){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:a}=this;"idle"!==a&&"finished"!==a&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){return Number(this.animation.effect?.getComputedTiming?.().duration||0)/1e3}get time(){return(Number(this.animation.currentTime)||0)/1e3}set time(a){this.finishedTime=null,this.animation.currentTime=cd(a)}get speed(){return this.animation.playbackRate}set speed(a){a<0&&(this.finishedTime=null),this.animation.playbackRate=a}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(a){this.animation.startTime=a}attachTimeline({timeline:a,observe:b}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,a&&c2())?(this.animation.timeline=a,ae):b(this)}}let c9={anticipate:cN,backInOut:cM,circInOut:cQ};class da extends c8{constructor(a){!function(a){"string"==typeof a.ease&&a.ease in c9&&(a.ease=c9[a.ease])}(a),cZ(a),super(a),a.startTime&&(this.startTime=a.startTime),this.options=a}updateMotionValue(a){let{motionValue:b,onUpdate:c,onComplete:d,element:e,...f}=this.options;if(!b)return;if(void 0!==a)return void b.set(a);let g=new c0({...f,autoplay:!1}),h=cd(this.finishedTime??this.time);b.setWithVelocity(g.sample(h-10).value,g.sample(h).value,10),g.stop()}}let db=(a,b)=>"zIndex"!==b&&!!("number"==typeof a||Array.isArray(a)||"string"==typeof a&&(aN.test(a)||"0"===a)&&!a.startsWith("url(")),dc=new Set(["opacity","clipPath","filter","transform"]),dd=c1(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class de extends c${constructor({autoplay:a=!0,delay:b=0,type:c="keyframes",repeat:d=0,repeatDelay:e=0,repeatType:f="loop",keyframes:g,name:h,motionValue:i,element:j,...k}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=a_.now();let l={autoplay:a,delay:b,type:c,repeat:d,repeatDelay:e,repeatType:f,name:h,motionValue:i,element:j,...k},m=j?.KeyframeResolver||at;this.keyframeResolver=new m(g,(a,b,c)=>this.onKeyframesResolved(a,b,l,!c),h,i,j),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(a,b,c,d){this.keyframeResolver=void 0;let{name:e,type:f,velocity:g,delay:h,isHandoff:i,onUpdate:j}=c;this.resolvedAt=a_.now(),!function(a,b,c,d){let e=a[0];if(null===e)return!1;if("display"===b||"visibility"===b)return!0;let f=a[a.length-1],g=db(e,b),h=db(f,b);return Y(g===h,`You are trying to animate ${b} from "${e}" to "${f}". "${g?f:e}" is not an animatable value.`,"value-not-animatable"),!!g&&!!h&&(function(a){let b=a[0];if(1===a.length)return!0;for(let c=0;c<a.length;c++)if(a[c]!==b)return!0}(a)||("spring"===c||c7(c))&&d)}(a,e,f,g)&&((af.instantAnimations||!h)&&j?.(cX(a,c,b)),a[0]=a[a.length-1],c.duration=0,c.repeat=0);let k={startTime:d?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:b,...c,keyframes:a},l=!i&&function(a){let{motionValue:b,name:c,repeatDelay:d,repeatType:e,damping:f,type:g}=a;if(!(b?.owner?.current instanceof HTMLElement))return!1;let{onUpdate:h,transformTemplate:i}=b.owner.getProps();return dd()&&c&&dc.has(c)&&("transform"!==c||!i)&&!h&&!d&&"mirror"!==e&&0!==f&&"inertia"!==g}(k)?new da({...k,element:k.motionValue.owner.current}):new c0(k);l.finished.then(()=>this.notifyFinished()).catch(ae),this.pendingTimeline&&(this.stopTimeline=l.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=l}get finished(){return this._animation?this.animation.finished:this._finished}then(a,b){return this.finished.finally(a).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),aq=!0,as(),ar(),aq=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(a){this.animation.time=a}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(a){this.animation.speed=a}get startTime(){return this.animation.startTime}attachTimeline(a){return this._animation?this.stopTimeline=this.animation.attachTimeline(a):this.pendingTimeline=a,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}let df=a=>null!==a,dg={type:"spring",stiffness:500,damping:25,restSpeed:10},dh={type:"keyframes",duration:.8},di={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},dj=(a,b,c,d={},e,f)=>h=>{let i=b8(d,a)||{},j=i.delay||d.delay||0,{elapsed:k=0}=d;k-=cd(j);let l={keyframes:Array.isArray(c)?c:[null,c],ease:"easeOut",velocity:b.getVelocity(),...i,delay:-k,onUpdate:a=>{b.set(a),i.onUpdate&&i.onUpdate(a)},onComplete:()=>{h(),i.onComplete&&i.onComplete()},name:a,motionValue:b,element:f?void 0:e};!function({when:a,delay:b,delayChildren:c,staggerChildren:d,staggerDirection:e,repeat:f,repeatType:g,repeatDelay:h,from:i,elapsed:j,...k}){return!!Object.keys(k).length}(i)&&Object.assign(l,((a,{keyframes:b})=>b.length>2?dh:g.has(a)?a.startsWith("scale")?{type:"spring",stiffness:550,damping:0===b[1]?2*Math.sqrt(550):30,restSpeed:10}:dg:di)(a,l)),l.duration&&(l.duration=cd(l.duration)),l.repeatDelay&&(l.repeatDelay=cd(l.repeatDelay)),void 0!==l.from&&(l.keyframes[0]=l.from);let m=!1;if(!1!==l.type&&(0!==l.duration||l.repeatDelay)||(l.duration=0,0===l.delay&&(m=!0)),(af.instantAnimations||af.skipAnimations)&&(m=!0,l.duration=0,l.delay=0),l.allowFlatten=!i.type&&!i.ease,m&&!f&&void 0!==b.get()){let a=function(a,{repeat:b,repeatType:c="loop"},d){let e=a.filter(df),f=b&&"loop"!==c&&b%2==1?0:e.length-1;return e[f]}(l.keyframes,i);if(void 0!==a)return void aj.update(()=>{l.onUpdate(a),l.onComplete()})}return i.isSync?new c0(l):new de(l)};function dk(a,b,{delay:c=0,transitionOverride:d,type:e}={}){let{transition:f=a.getDefaultTransition(),transitionEnd:g,...h}=b;d&&(f=d);let i=[],j=e&&a.animationState&&a.animationState.getState()[e];for(let b in h){let d=a.getValue(b,a.latestValues[b]??null),e=h[b];if(void 0===e||j&&function({protectedKeys:a,needsAnimating:b},c){let d=a.hasOwnProperty(c)&&!0!==b[c];return b[c]=!1,d}(j,b))continue;let g={delay:c,...b8(f||{},b)},k=d.get();if(void 0!==k&&!d.isAnimating&&!Array.isArray(e)&&e===k&&!g.velocity)continue;let l=!1;if(window.MotionHandoffAnimation){let c=a.props[b3];if(c){let a=window.MotionHandoffAnimation(c,b,aj);null!==a&&(g.startTime=a,l=!0)}}ca(a,b),d.start(dj(b,d,e,a.shouldReduceMotion&&J.has(b)?{type:!1}:g,a,l));let m=d.animation;m&&i.push(m)}return g&&Promise.all(i).then(()=>{aj.update(()=>{g&&function(a,b){let{transitionEnd:c={},transition:d={},...e}=b7(a,b)||{};for(let b in e={...e,...c}){var f;let c=b9(f=e[b])?f[f.length-1]||0:f;a.hasValue(b)?a.getValue(b).set(c):a.addValue(b,a5(c))}}(a,g)})}),i}function dl(a,b,c={}){let d=b7(a,b,"exit"===c.type?a.presenceContext?.custom:void 0),{transition:e=a.getDefaultTransition()||{}}=d||{};c.transitionOverride&&(e=c.transitionOverride);let f=d?()=>Promise.all(dk(a,d,c)):()=>Promise.resolve(),g=a.variantChildren&&a.variantChildren.size?(d=0)=>{let{delayChildren:f=0,staggerChildren:g,staggerDirection:h}=e;return function(a,b,c=0,d=0,e=0,f=1,g){let h=[],i=a.variantChildren.size,j=(i-1)*e,k="function"==typeof d,l=k?a=>d(a,i):1===f?(a=0)=>a*e:(a=0)=>j-a*e;return Array.from(a.variantChildren).sort(dm).forEach((a,e)=>{a.notify("AnimationStart",b),h.push(dl(a,b,{...g,delay:c+(k?0:d)+l(e)}).then(()=>a.notify("AnimationComplete",b)))}),Promise.all(h)}(a,b,d,f,g,h,c)}:()=>Promise.resolve(),{when:h}=e;if(!h)return Promise.all([f(),g(c.delay)]);{let[a,b]="beforeChildren"===h?[f,g]:[g,f];return a().then(()=>b())}}function dm(a,b){return a.sortNodePosition(b)}function dn(a,b){if(!Array.isArray(b))return!1;let c=b.length;if(c!==a.length)return!1;for(let d=0;d<c;d++)if(b[d]!==a[d])return!1;return!0}let dp=bl.length,dq=[...bk].reverse(),dr=bk.length;function ds(a=!1){return{isActive:a,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function dt(){return{animate:ds(!0),whileInView:ds(),whileHover:ds(),whileTap:ds(),whileDrag:ds(),whileFocus:ds(),exit:ds()}}class du{constructor(a){this.isMounted=!1,this.node=a}update(){}}class dv extends du{constructor(a){super(a),a.animationState||(a.animationState=function(a){let b=b=>Promise.all(b.map(({animation:b,options:c})=>(function(a,b,c={}){let d;if(a.notify("AnimationStart",b),Array.isArray(b))d=Promise.all(b.map(b=>dl(a,b,c)));else if("string"==typeof b)d=dl(a,b,c);else{let e="function"==typeof b?b7(a,b,c.custom):b;d=Promise.all(dk(a,e,c))}return d.then(()=>{a.notify("AnimationComplete",b)})})(a,b,c))),c=dt(),d=!0,e=b=>(c,d)=>{let e=b7(a,d,"exit"===b?a.presenceContext?.custom:void 0);if(e){let{transition:a,transitionEnd:b,...d}=e;c={...c,...d,...b}}return c};function f(f){let{props:g}=a,h=function a(b){if(!b)return;if(!b.isControllingVariants){let c=b.parent&&a(b.parent)||{};return void 0!==b.props.initial&&(c.initial=b.props.initial),c}let c={};for(let a=0;a<dp;a++){let d=bl[a],e=b.props[d];(bj(e)||!1===e)&&(c[d]=e)}return c}(a.parent)||{},i=[],j=new Set,k={},l=1/0;for(let b=0;b<dr;b++){var m,n;let o=dq[b],p=c[o],q=void 0!==g[o]?g[o]:h[o],r=bj(q),s=o===f?p.isActive:null;!1===s&&(l=b);let t=q===h[o]&&q!==g[o]&&r;if(t&&d&&a.manuallyAnimateOnMount&&(t=!1),p.protectedKeys={...k},!p.isActive&&null===s||!q&&!p.prevProp||bi(q)||"boolean"==typeof q)continue;let u=(m=p.prevProp,"string"==typeof(n=q)?n!==m:!!Array.isArray(n)&&!dn(n,m)),v=u||o===f&&p.isActive&&!t&&r||b>l&&r,w=!1,x=Array.isArray(q)?q:[q],y=x.reduce(e(o),{});!1===s&&(y={});let{prevResolvedValues:z={}}=p,A={...z,...y},B=b=>{v=!0,j.has(b)&&(w=!0,j.delete(b)),p.needsAnimating[b]=!0;let c=a.getValue(b);c&&(c.liveStyle=!1)};for(let a in A){let b=y[a],c=z[a];if(!k.hasOwnProperty(a))(b9(b)&&b9(c)?dn(b,c):b===c)?void 0!==b&&j.has(a)?B(a):p.protectedKeys[a]=!0:null!=b?B(a):j.add(a)}p.prevProp=q,p.prevResolvedValues=y,p.isActive&&(k={...k,...y}),d&&a.blockInitialAnimation&&(v=!1);let C=!(t&&u)||w;v&&C&&i.push(...x.map(a=>({animation:a,options:{type:o}})))}if(j.size){let b={};if("boolean"!=typeof g.initial){let c=b7(a,Array.isArray(g.initial)?g.initial[0]:g.initial);c&&c.transition&&(b.transition=c.transition)}j.forEach(c=>{let d=a.getBaseTarget(c),e=a.getValue(c);e&&(e.liveStyle=!0),b[c]=d??null}),i.push({animation:b})}let o=!!i.length;return d&&(!1===g.initial||g.initial===g.animate)&&!a.manuallyAnimateOnMount&&(o=!1),d=!1,o?b(i):Promise.resolve()}return{animateChanges:f,setActive:function(b,d){if(c[b].isActive===d)return Promise.resolve();a.variantChildren?.forEach(a=>a.animationState?.setActive(b,d)),c[b].isActive=d;let e=f(b);for(let a in c)c[a].protectedKeys={};return e},setAnimateFunction:function(c){b=c(a)},getState:()=>c,reset:()=>{c=dt(),d=!0}}}(a))}updateAnimationControlsSubscription(){let{animate:a}=this.node.getProps();bi(a)&&(this.unmountControls=a.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:a}=this.node.getProps(),{animate:b}=this.node.prevProps||{};a!==b&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let dw=0;class dx extends du{constructor(){super(...arguments),this.id=dw++}update(){if(!this.node.presenceContext)return;let{isPresent:a,onExitComplete:b}=this.node.presenceContext,{isPresent:c}=this.node.prevPresenceContext||{};if(!this.node.animationState||a===c)return;let d=this.node.animationState.setActive("exit",!a);b&&!a&&d.then(()=>{b(this.id)})}mount(){let{register:a,onExitComplete:b}=this.node.presenceContext||{};b&&b(this.id),a&&(this.unmount=a(this.id))}unmount(){}}let dy={x:!1,y:!1};function dz(a,b,c,d={passive:!0}){return a.addEventListener(b,c,d),()=>a.removeEventListener(b,c)}let dA=a=>"mouse"===a.pointerType?"number"!=typeof a.button||a.button<=0:!1!==a.isPrimary;function dB(a){return{point:{x:a.pageX,y:a.pageY}}}function dC(a,b,c,d){return dz(a,b,a=>dA(a)&&c(a,dB(a)),d)}function dD(a){return a.max-a.min}function dE(a,b,c,d=.5){a.origin=d,a.originPoint=x(b.min,b.max,a.origin),a.scale=dD(c)/dD(b),a.translate=x(c.min,c.max,a.origin)-a.originPoint,(a.scale>=.9999&&a.scale<=1.0001||isNaN(a.scale))&&(a.scale=1),(a.translate>=-.01&&a.translate<=.01||isNaN(a.translate))&&(a.translate=0)}function dF(a,b,c,d){dE(a.x,b.x,c.x,d?d.originX:void 0),dE(a.y,b.y,c.y,d?d.originY:void 0)}function dG(a,b,c){a.min=c.min+b.min,a.max=a.min+dD(b)}function dH(a,b,c){a.min=b.min-c.min,a.max=a.min+dD(b)}function dI(a,b,c){dH(a.x,b.x,c.x),dH(a.y,b.y,c.y)}function dJ(a){return[a("x"),a("y")]}let dK=({current:a})=>a?a.ownerDocument.defaultView:null,dL=(a,b)=>Math.abs(a-b);class dM{constructor(a,b,{transformPagePoint:c,contextWindow:d=window,dragSnapToOrigin:e=!1,distanceThreshold:f=3}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let a=dP(this.lastMoveEventInfo,this.history),b=null!==this.startEvent,c=function(a,b){return Math.sqrt(dL(a.x,b.x)**2+dL(a.y,b.y)**2)}(a.offset,{x:0,y:0})>=this.distanceThreshold;if(!b&&!c)return;let{point:d}=a,{timestamp:e}=al;this.history.push({...d,timestamp:e});let{onStart:f,onMove:g}=this.handlers;b||(f&&f(this.lastMoveEvent,a),this.startEvent=this.lastMoveEvent),g&&g(this.lastMoveEvent,a)},this.handlePointerMove=(a,b)=>{this.lastMoveEvent=a,this.lastMoveEventInfo=dN(b,this.transformPagePoint),aj.update(this.updatePoint,!0)},this.handlePointerUp=(a,b)=>{this.end();let{onEnd:c,onSessionEnd:d,resumeAnimation:e}=this.handlers;if(this.dragSnapToOrigin&&e&&e(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let f=dP("pointercancel"===a.type?this.lastMoveEventInfo:dN(b,this.transformPagePoint),this.history);this.startEvent&&c&&c(a,f),d&&d(a,f)},!dA(a))return;this.dragSnapToOrigin=e,this.handlers=b,this.transformPagePoint=c,this.distanceThreshold=f,this.contextWindow=d||window;let g=dN(dB(a),this.transformPagePoint),{point:h}=g,{timestamp:i}=al;this.history=[{...h,timestamp:i}];let{onSessionStart:j}=b;j&&j(a,dP(g,this.history)),this.removeListeners=cc(dC(this.contextWindow,"pointermove",this.handlePointerMove),dC(this.contextWindow,"pointerup",this.handlePointerUp),dC(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(a){this.handlers=a}end(){this.removeListeners&&this.removeListeners(),ak(this.updatePoint)}}function dN(a,b){return b?{point:b(a.point)}:a}function dO(a,b){return{x:a.x-b.x,y:a.y-b.y}}function dP({point:a},b){return{point:a,delta:dO(a,dQ(b)),offset:dO(a,b[0]),velocity:function(a,b){if(a.length<2)return{x:0,y:0};let c=a.length-1,d=null,e=dQ(a);for(;c>=0&&(d=a[c],!(e.timestamp-d.timestamp>cd(.1)));)c--;if(!d)return{x:0,y:0};let f=(e.timestamp-d.timestamp)/1e3;if(0===f)return{x:0,y:0};let g={x:(e.x-d.x)/f,y:(e.y-d.y)/f};return g.x===1/0&&(g.x=0),g.y===1/0&&(g.y=0),g}(b,.1)}}function dQ(a){return a[a.length-1]}function dR(a,b,c){return{min:void 0!==b?a.min+b:void 0,max:void 0!==c?a.max+c-(a.max-a.min):void 0}}function dS(a,b){let c=b.min-a.min,d=b.max-a.max;return b.max-b.min<a.max-a.min&&([c,d]=[d,c]),{min:c,max:d}}function dT(a,b,c){return{min:dU(a,b),max:dU(a,c)}}function dU(a,b){return"number"==typeof a?a:a[b]||0}let dV=new WeakMap;class dW{constructor(a){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=bd(),this.latestPointerEvent=null,this.latestPanInfo=null,this.visualElement=a}start(a,{snapToCursor:b=!1,distanceThreshold:c}={}){let{presenceContext:d}=this.visualElement;if(d&&!1===d.isPresent)return;let{dragSnapToOrigin:e}=this.getProps();this.panSession=new dM(a,{onSessionStart:a=>{let{dragSnapToOrigin:c}=this.getProps();c?this.pauseAnimation():this.stopAnimation(),b&&this.snapToCursor(dB(a).point)},onStart:(a,b)=>{let{drag:c,dragPropagation:d,onDragStart:e}=this.getProps();if(c&&!d&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(a){if("x"===a||"y"===a)if(dy[a])return null;else return dy[a]=!0,()=>{dy[a]=!1};return dy.x||dy.y?null:(dy.x=dy.y=!0,()=>{dy.x=dy.y=!1})}(c),!this.openDragLock))return;this.latestPointerEvent=a,this.latestPanInfo=b,this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),dJ(a=>{let b=this.getAxisMotionValue(a).get()||0;if(Q.test(b)){let{projection:c}=this.visualElement;if(c&&c.layout){let d=c.layout.layoutBox[a];d&&(b=dD(d)*(parseFloat(b)/100))}}this.originPoint[a]=b}),e&&aj.postRender(()=>e(a,b)),ca(this.visualElement,"transform");let{animationState:f}=this.visualElement;f&&f.setActive("whileDrag",!0)},onMove:(a,b)=>{this.latestPointerEvent=a,this.latestPanInfo=b;let{dragPropagation:c,dragDirectionLock:d,onDirectionLock:e,onDrag:f}=this.getProps();if(!c&&!this.openDragLock)return;let{offset:g}=b;if(d&&null===this.currentDirection){this.currentDirection=function(a,b=10){let c=null;return Math.abs(a.y)>b?c="y":Math.abs(a.x)>b&&(c="x"),c}(g),null!==this.currentDirection&&e&&e(this.currentDirection);return}this.updateAxis("x",b.point,g),this.updateAxis("y",b.point,g),this.visualElement.render(),f&&f(a,b)},onSessionEnd:(a,b)=>{this.latestPointerEvent=a,this.latestPanInfo=b,this.stop(a,b),this.latestPointerEvent=null,this.latestPanInfo=null},resumeAnimation:()=>dJ(a=>"paused"===this.getAnimationState(a)&&this.getAxisMotionValue(a).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:e,distanceThreshold:c,contextWindow:dK(this.visualElement)})}stop(a,b){let c=a||this.latestPointerEvent,d=b||this.latestPanInfo,e=this.isDragging;if(this.cancel(),!e||!d||!c)return;let{velocity:f}=d;this.startAnimation(f);let{onDragEnd:g}=this.getProps();g&&aj.postRender(()=>g(c,d))}cancel(){this.isDragging=!1;let{projection:a,animationState:b}=this.visualElement;a&&(a.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:c}=this.getProps();!c&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),b&&b.setActive("whileDrag",!1)}updateAxis(a,b,c){let{drag:d}=this.getProps();if(!c||!dX(a,d,this.currentDirection))return;let e=this.getAxisMotionValue(a),f=this.originPoint[a]+c[a];this.constraints&&this.constraints[a]&&(f=function(a,{min:b,max:c},d){return void 0!==b&&a<b?a=d?x(b,a,d.min):Math.max(a,b):void 0!==c&&a>c&&(a=d?x(c,a,d.max):Math.min(a,c)),a}(f,this.constraints[a],this.elastic[a])),e.set(f)}resolveConstraints(){let{dragConstraints:a,dragElastic:b}=this.getProps(),c=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,d=this.constraints;a&&b2(a)?this.constraints||(this.constraints=this.resolveRefConstraints()):a&&c?this.constraints=function(a,{top:b,left:c,bottom:d,right:e}){return{x:dR(a.x,c,e),y:dR(a.y,b,d)}}(c.layoutBox,a):this.constraints=!1,this.elastic=function(a=.35){return!1===a?a=0:!0===a&&(a=.35),{x:dT(a,"left","right"),y:dT(a,"top","bottom")}}(b),d!==this.constraints&&c&&this.constraints&&!this.hasMutatedConstraints&&dJ(a=>{!1!==this.constraints&&this.getAxisMotionValue(a)&&(this.constraints[a]=function(a,b){let c={};return void 0!==b.min&&(c.min=b.min-a.min),void 0!==b.max&&(c.max=b.max-a.min),c}(c.layoutBox[a],this.constraints[a]))})}resolveRefConstraints(){var a;let{dragConstraints:b,onMeasureDragConstraints:c}=this.getProps();if(!b||!b2(b))return!1;let d=b.current;Z(null!==d,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.","drag-constraints-ref");let{projection:e}=this.visualElement;if(!e||!e.layout)return!1;let f=function(a,b,c){let d=I(a,c),{scroll:e}=b;return e&&(F(d.x,e.offset.x),F(d.y,e.offset.y)),d}(d,e.root,this.visualElement.getTransformPagePoint()),g=(a=e.layout.layoutBox,{x:dS(a.x,f.x),y:dS(a.y,f.y)});if(c){let a=c(function({x:a,y:b}){return{top:b.min,right:a.max,bottom:b.max,left:a.min}}(g));this.hasMutatedConstraints=!!a,a&&(g=w(a))}return g}startAnimation(a){let{drag:b,dragMomentum:c,dragElastic:d,dragTransition:e,dragSnapToOrigin:f,onDragTransitionEnd:g}=this.getProps(),h=this.constraints||{};return Promise.all(dJ(g=>{if(!dX(g,b,this.currentDirection))return;let i=h&&h[g]||{};f&&(i={min:0,max:0});let j={type:"inertia",velocity:c?a[g]:0,bounceStiffness:d?200:1e6,bounceDamping:d?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...e,...i};return this.startAxisValueAnimation(g,j)})).then(g)}startAxisValueAnimation(a,b){let c=this.getAxisMotionValue(a);return ca(this.visualElement,a),c.start(dj(a,c,0,b,this.visualElement,!1))}stopAnimation(){dJ(a=>this.getAxisMotionValue(a).stop())}pauseAnimation(){dJ(a=>this.getAxisMotionValue(a).animation?.pause())}getAnimationState(a){return this.getAxisMotionValue(a).animation?.state}getAxisMotionValue(a){let b=`_drag${a.toUpperCase()}`,c=this.visualElement.getProps();return c[b]||this.visualElement.getValue(a,(c.initial?c.initial[a]:void 0)||0)}snapToCursor(a){dJ(b=>{let{drag:c}=this.getProps();if(!dX(b,c,this.currentDirection))return;let{projection:d}=this.visualElement,e=this.getAxisMotionValue(b);if(d&&d.layout){let{min:c,max:f}=d.layout.layoutBox[b];e.set(a[b]-x(c,f,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:a,dragConstraints:b}=this.getProps(),{projection:c}=this.visualElement;if(!b2(b)||!c||!this.constraints)return;this.stopAnimation();let d={x:0,y:0};dJ(a=>{let b=this.getAxisMotionValue(a);if(b&&!1!==this.constraints){let c=b.get();d[a]=function(a,b){let c=.5,d=dD(a),e=dD(b);return e>d?c=cU(b.min,b.max-d,a.min):d>e&&(c=cU(a.min,a.max-e,b.min)),K(0,1,c)}({min:c,max:c},this.constraints[a])}});let{transformTemplate:e}=this.visualElement.getProps();this.visualElement.current.style.transform=e?e({},""):"none",c.root&&c.root.updateScroll(),c.updateLayout(),this.resolveConstraints(),dJ(b=>{if(!dX(b,a,null))return;let c=this.getAxisMotionValue(b),{min:e,max:f}=this.constraints[b];c.set(x(e,f,d[b]))})}addListeners(){if(!this.visualElement.current)return;dV.set(this.visualElement,this);let a=dC(this.visualElement.current,"pointerdown",a=>{let{drag:b,dragListener:c=!0}=this.getProps();b&&c&&this.start(a)}),b=()=>{let{dragConstraints:a}=this.getProps();b2(a)&&a.current&&(this.constraints=this.resolveRefConstraints())},{projection:c}=this.visualElement,d=c.addEventListener("measure",b);c&&!c.layout&&(c.root&&c.root.updateScroll(),c.updateLayout()),aj.read(b);let e=dz(window,"resize",()=>this.scalePositionWithinConstraints()),f=c.addEventListener("didUpdate",({delta:a,hasLayoutChanged:b})=>{this.isDragging&&b&&(dJ(b=>{let c=this.getAxisMotionValue(b);c&&(this.originPoint[b]+=a[b].translate,c.set(c.get()+a[b].translate))}),this.visualElement.render())});return()=>{e(),a(),d(),f&&f()}}getProps(){let a=this.visualElement.getProps(),{drag:b=!1,dragDirectionLock:c=!1,dragPropagation:d=!1,dragConstraints:e=!1,dragElastic:f=.35,dragMomentum:g=!0}=a;return{...a,drag:b,dragDirectionLock:c,dragPropagation:d,dragConstraints:e,dragElastic:f,dragMomentum:g}}}function dX(a,b,c){return(!0===b||b===a)&&(null===c||c===a)}class dY extends du{constructor(a){super(a),this.removeGroupControls=ae,this.removeListeners=ae,this.controls=new dW(a)}mount(){let{dragControls:a}=this.node.getProps();a&&(this.removeGroupControls=a.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||ae}unmount(){this.removeGroupControls(),this.removeListeners()}}let dZ=a=>(b,c)=>{a&&aj.postRender(()=>a(b,c))};class d$ extends du{constructor(){super(...arguments),this.removePointerDownListener=ae}onPointerDown(a){this.session=new dM(a,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:dK(this.node)})}createPanHandlers(){let{onPanSessionStart:a,onPanStart:b,onPan:c,onPanEnd:d}=this.node.getProps();return{onSessionStart:dZ(a),onStart:dZ(b),onMove:c,onEnd:(a,b)=>{delete this.session,d&&aj.postRender(()=>d(a,b))}}}mount(){this.removePointerDownListener=dC(this.node.current,"pointerdown",a=>this.onPointerDown(a))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}let d_={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function d0(a,b){return b.max===b.min?0:a/(b.max-b.min)*100}let d1={correct:(a,b)=>{if(!b.target)return a;if("string"==typeof a)if(!R.test(a))return a;else a=parseFloat(a);let c=d0(a,b.target.x),d=d0(a,b.target.y);return`${c}% ${d}%`}},d2=!1;class d3 extends e.Component{componentDidMount(){let{visualElement:a,layoutGroup:b,switchLayoutGroup:c,layoutId:d}=this.props,{projection:e}=a;for(let a in d5)by[a]=d5[a],s(a)&&(by[a].isCSSVariable=!0);e&&(b.group&&b.group.add(e),c&&c.register&&d&&c.register(e),d2&&e.root.didUpdate(),e.addEventListener("animationComplete",()=>{this.safeToRemove()}),e.setOptions({...e.options,onExitComplete:()=>this.safeToRemove()})),d_.hasEverUpdated=!0}getSnapshotBeforeUpdate(a){let{layoutDependency:b,visualElement:c,drag:d,isPresent:e}=this.props,{projection:f}=c;return f&&(f.isPresent=e,d2=!0,d||a.layoutDependency!==b||void 0===b||a.isPresent!==e?f.willUpdate():this.safeToRemove(),a.isPresent!==e&&(e?f.promote():f.relegate()||aj.postRender(()=>{let a=f.getStack();a&&a.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:a}=this.props.visualElement;a&&(a.root.didUpdate(),a7.postRender(()=>{!a.currentAnimation&&a.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:a,layoutGroup:b,switchLayoutGroup:c}=this.props,{projection:d}=a;d&&(d.scheduleCheckAfterUnmount(),b&&b.group&&b.group.remove(d),c&&c.deregister&&c.deregister(d))}safeToRemove(){let{safeToRemove:a}=this.props;a&&a()}render(){return null}}function d4(a){let[b,c]=function(a=!0){let b=(0,e.useContext)(bY);if(null===b)return[!0,null];let{isPresent:c,onExitComplete:d,register:f}=b,g=(0,e.useId)();(0,e.useEffect)(()=>{if(a)return f(g)},[a]);let h=(0,e.useCallback)(()=>a&&d&&d(g),[g,d,a]);return!c&&d?[!1,h]:[!0]}(),d=(0,e.useContext)(bN);return(0,bM.jsx)(d3,{...a,layoutGroup:d,switchLayoutGroup:(0,e.useContext)(b4),isPresent:b,safeToRemove:c})}let d5={borderRadius:{...d1,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:d1,borderTopRightRadius:d1,borderBottomLeftRadius:d1,borderBottomRightRadius:d1,boxShadow:{correct:(a,{treeScale:b,projectionDelta:c})=>{let d=aN.parse(a);if(d.length>5)return a;let e=aN.createTransformer(a),f=+("number"!=typeof d[0]),g=c.x.scale*b.x,h=c.y.scale*b.y;d[0+f]/=g,d[1+f]/=h;let i=x(g,h,.5);return"number"==typeof d[2+f]&&(d[2+f]/=i),"number"==typeof d[3+f]&&(d[3+f]/=i),e(d)}}};function d6(a){return"object"==typeof a&&null!==a}function d7(a){return d6(a)&&"ownerSVGElement"in a}let d8=(a,b)=>a.depth-b.depth;class d9{constructor(){this.children=[],this.isDirty=!1}add(a){a0(this.children,a),this.isDirty=!0}remove(a){a1(this.children,a),this.isDirty=!0}forEach(a){this.isDirty&&this.children.sort(d8),this.isDirty=!1,this.children.forEach(a)}}let ea=["TopLeft","TopRight","BottomLeft","BottomRight"],eb=ea.length,ec=a=>"string"==typeof a?parseFloat(a):a,ed=a=>"number"==typeof a||R.test(a);function ee(a,b){return void 0!==a[b]?a[b]:a.borderRadius}let ef=eh(0,.5,cP),eg=eh(.5,.95,ae);function eh(a,b,c){return d=>d<a?0:d>b?1:c(cU(a,b,d))}function ei(a,b){a.min=b.min,a.max=b.max}function ej(a,b){ei(a.x,b.x),ei(a.y,b.y)}function ek(a,b){a.translate=b.translate,a.scale=b.scale,a.originPoint=b.originPoint,a.origin=b.origin}function el(a,b,c,d,e){return a-=b,a=d+1/c*(a-d),void 0!==e&&(a=d+1/e*(a-d)),a}function em(a,b,[c,d,e],f,g){!function(a,b=0,c=1,d=.5,e,f=a,g=a){if(Q.test(b)&&(b=parseFloat(b),b=x(g.min,g.max,b/100)-g.min),"number"!=typeof b)return;let h=x(f.min,f.max,d);a===f&&(h-=b),a.min=el(a.min,b,c,h,e),a.max=el(a.max,b,c,h,e)}(a,b[c],b[d],b[e],b.scale,f,g)}let en=["x","scaleX","originX"],eo=["y","scaleY","originY"];function ep(a,b,c,d){em(a.x,b,en,c?c.x:void 0,d?d.x:void 0),em(a.y,b,eo,c?c.y:void 0,d?d.y:void 0)}function eq(a){return 0===a.translate&&1===a.scale}function er(a){return eq(a.x)&&eq(a.y)}function es(a,b){return a.min===b.min&&a.max===b.max}function et(a,b){return Math.round(a.min)===Math.round(b.min)&&Math.round(a.max)===Math.round(b.max)}function eu(a,b){return et(a.x,b.x)&&et(a.y,b.y)}function ev(a){return dD(a.x)/dD(a.y)}function ew(a,b){return a.translate===b.translate&&a.scale===b.scale&&a.originPoint===b.originPoint}class ex{constructor(){this.members=[]}add(a){a0(this.members,a),a.scheduleRender()}remove(a){if(a1(this.members,a),a===this.prevLead&&(this.prevLead=void 0),a===this.lead){let a=this.members[this.members.length-1];a&&this.promote(a)}}relegate(a){let b,c=this.members.findIndex(b=>a===b);if(0===c)return!1;for(let a=c;a>=0;a--){let c=this.members[a];if(!1!==c.isPresent){b=c;break}}return!!b&&(this.promote(b),!0)}promote(a,b){let c=this.lead;if(a!==c&&(this.prevLead=c,this.lead=a,a.show(),c)){c.instance&&c.scheduleRender(),a.scheduleRender(),a.resumeFrom=c,b&&(a.resumeFrom.preserveOpacity=!0),c.snapshot&&(a.snapshot=c.snapshot,a.snapshot.latestValues=c.animationValues||c.latestValues),a.root&&a.root.isUpdating&&(a.isLayoutDirty=!0);let{crossfade:d}=a.options;!1===d&&c.hide()}}exitAnimationComplete(){this.members.forEach(a=>{let{options:b,resumingFrom:c}=a;b.onExitComplete&&b.onExitComplete(),c&&c.options.onExitComplete&&c.options.onExitComplete()})}scheduleRender(){this.members.forEach(a=>{a.instance&&a.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let ey={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},ez=["","X","Y","Z"],eA=0;function eB(a,b,c,d){let{latestValues:e}=b;e[a]&&(c[a]=e[a],b.setStaticValue(a,0),d&&(d[a]=0))}function eC({attachResizeListener:a,defaultParent:b,measureScroll:c,checkIsScrollRoot:d,resetTransform:e}){return class{constructor(a={},c=b?.()){this.id=eA++,this.animationId=0,this.animationCommitId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,ah.value&&(ey.nodes=ey.calculatedTargetDeltas=ey.calculatedProjections=0),this.nodes.forEach(eF),this.nodes.forEach(eM),this.nodes.forEach(eN),this.nodes.forEach(eG),ah.addProjectionMetrics&&ah.addProjectionMetrics(ey)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=a,this.root=c?c.root||c:this,this.path=c?[...c.path,c]:[],this.parent=c,this.depth=c?c.depth+1:0;for(let a=0;a<this.path.length;a++)this.path[a].shouldResetTransform=!0;this.root===this&&(this.nodes=new d9)}addEventListener(a,b){return this.eventHandlers.has(a)||this.eventHandlers.set(a,new a2),this.eventHandlers.get(a).add(b)}notifyListeners(a,...b){let c=this.eventHandlers.get(a);c&&c.notify(...b)}hasListeners(a){return this.eventHandlers.has(a)}mount(b){if(this.instance)return;this.isSVG=d7(b)&&!(d7(b)&&"svg"===b.tagName),this.instance=b;let{layoutId:c,layout:d,visualElement:e}=this.options;if(e&&!e.current&&e.mount(b),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(d||c)&&(this.isLayoutDirty=!0),a){let c,d=0,e=()=>this.root.updateBlockedByResize=!1;aj.read(()=>{d=window.innerWidth}),a(b,()=>{let a=window.innerWidth;a!==d&&(d=a,this.root.updateBlockedByResize=!0,c&&c(),c=function(a,b){let c=a_.now(),d=({timestamp:b})=>{let e=b-c;e>=250&&(ak(d),a(e-250))};return aj.setup(d,!0),()=>ak(d)}(e,250),d_.hasAnimatedSinceResize&&(d_.hasAnimatedSinceResize=!1,this.nodes.forEach(eL)))})}c&&this.root.registerSharedNode(c,this),!1!==this.options.animate&&e&&(c||d)&&this.addEventListener("didUpdate",({delta:a,hasLayoutChanged:b,hasRelativeLayoutChanged:c,layout:d})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let f=this.options.transition||e.getDefaultTransition()||eT,{onLayoutAnimationStart:g,onLayoutAnimationComplete:h}=e.getProps(),i=!this.targetLayout||!eu(this.targetLayout,d),j=!b&&c;if(this.options.layoutRoot||this.resumeFrom||j||b&&(i||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);let b={...b8(f,"layout"),onPlay:g,onComplete:h};(e.shouldReduceMotion||this.options.layoutRoot)&&(b.delay=0,b.type=!1),this.startAnimation(b),this.setAnimationOrigin(a,j)}else b||eL(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=d})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let a=this.getStack();a&&a.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),ak(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(eO),this.animationId++)}getTransformTemplate(){let{visualElement:a}=this.options;return a&&a.getProps().transformTemplate}willUpdate(a=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function a(b){if(b.hasCheckedOptimisedAppear=!0,b.root===b)return;let{visualElement:c}=b.options;if(!c)return;let d=c.props[b3];if(window.MotionHasOptimisedAnimation(d,"transform")){let{layout:a,layoutId:c}=b.options;window.MotionCancelOptimisedAnimation(d,"transform",aj,!(a||c))}let{parent:e}=b;e&&!e.hasCheckedOptimisedAppear&&a(e)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let a=0;a<this.path.length;a++){let b=this.path[a];b.shouldResetTransform=!0,b.updateScroll("snapshot"),b.options.layoutRoot&&b.willUpdate(!1)}let{layoutId:b,layout:c}=this.options;if(void 0===b&&!c)return;let d=this.getTransformTemplate();this.prevTransformTemplateValue=d?d(this.latestValues,""):void 0,this.updateSnapshot(),a&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(eI);return}if(this.animationId<=this.animationCommitId)return void this.nodes.forEach(eJ);this.animationCommitId=this.animationId,this.isUpdating?(this.isUpdating=!1,this.nodes.forEach(eK),this.nodes.forEach(eD),this.nodes.forEach(eE)):this.nodes.forEach(eJ),this.clearAllSnapshots();let a=a_.now();al.delta=K(0,1e3/60,a-al.timestamp),al.timestamp=a,al.isProcessing=!0,am.update.process(al),am.preRender.process(al),am.render.process(al),al.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,a7.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(eH),this.sharedNodes.forEach(eP)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,aj.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){aj.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||dD(this.snapshot.measuredBox.x)||dD(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let a=0;a<this.path.length;a++)this.path[a].updateScroll();let a=this.layout;this.layout=this.measure(!1),this.layoutCorrected=bd(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:b}=this.options;b&&b.notify("LayoutMeasure",this.layout.layoutBox,a?a.layoutBox:void 0)}updateScroll(a="measure"){let b=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===a&&(b=!1),b&&this.instance){let b=d(this.instance);this.scroll={animationId:this.root.animationId,phase:a,isRoot:b,offset:c(this.instance),wasRoot:this.scroll?this.scroll.isRoot:b}}}resetTransform(){if(!e)return;let a=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,b=this.projectionDelta&&!er(this.projectionDelta),c=this.getTransformTemplate(),d=c?c(this.latestValues,""):void 0,f=d!==this.prevTransformTemplateValue;a&&this.instance&&(b||A(this.latestValues)||f)&&(e(this.instance,d),this.shouldResetTransform=!1,this.scheduleRender())}measure(a=!0){var b;let c=this.measurePageBox(),d=this.removeElementScroll(c);return a&&(d=this.removeTransform(d)),eW((b=d).x),eW(b.y),{animationId:this.root.animationId,measuredBox:c,layoutBox:d,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:a}=this.options;if(!a)return bd();let b=a.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(eY))){let{scroll:a}=this.root;a&&(F(b.x,a.offset.x),F(b.y,a.offset.y))}return b}removeElementScroll(a){let b=bd();if(ej(b,a),this.scroll?.wasRoot)return b;for(let c=0;c<this.path.length;c++){let d=this.path[c],{scroll:e,options:f}=d;d!==this.root&&e&&f.layoutScroll&&(e.wasRoot&&ej(b,a),F(b.x,e.offset.x),F(b.y,e.offset.y))}return b}applyTransform(a,b=!1){let c=bd();ej(c,a);for(let a=0;a<this.path.length;a++){let d=this.path[a];!b&&d.options.layoutScroll&&d.scroll&&d!==d.root&&H(c,{x:-d.scroll.offset.x,y:-d.scroll.offset.y}),A(d.latestValues)&&H(c,d.latestValues)}return A(this.latestValues)&&H(c,this.latestValues),c}removeTransform(a){let b=bd();ej(b,a);for(let a=0;a<this.path.length;a++){let c=this.path[a];if(!c.instance||!A(c.latestValues))continue;z(c.latestValues)&&c.updateSnapshot();let d=bd();ej(d,c.measurePageBox()),ep(b,c.latestValues,c.snapshot?c.snapshot.layoutBox:void 0,d)}return A(this.latestValues)&&ep(b,this.latestValues),b}setTargetDelta(a){this.targetDelta=a,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(a){this.options={...this.options,...a,crossfade:void 0===a.crossfade||a.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==al.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(a=!1){let b=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=b.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=b.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=b.isSharedProjectionDirty);let c=!!this.resumingFrom||this!==b;if(!(a||c&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:d,layoutId:e}=this.options;if(this.layout&&(d||e)){if(this.resolvedRelativeTargetAt=al.timestamp,!this.targetDelta&&!this.relativeTarget){let a=this.getClosestProjectingParent();a&&a.layout&&1!==this.animationProgress?(this.relativeParent=a,this.forceRelativeParentToResolveTarget(),this.relativeTarget=bd(),this.relativeTargetOrigin=bd(),dI(this.relativeTargetOrigin,this.layout.layoutBox,a.layout.layoutBox),ej(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=bd(),this.targetWithTransforms=bd()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target){var f,g,h;this.forceRelativeParentToResolveTarget(),f=this.target,g=this.relativeTarget,h=this.relativeParent.target,dG(f.x,g.x,h.x),dG(f.y,g.y,h.y)}else this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):ej(this.target,this.layout.layoutBox),E(this.target,this.targetDelta)):ej(this.target,this.layout.layoutBox);if(this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let a=this.getClosestProjectingParent();a&&!!a.resumingFrom==!!this.resumingFrom&&!a.options.layoutScroll&&a.target&&1!==this.animationProgress?(this.relativeParent=a,this.forceRelativeParentToResolveTarget(),this.relativeTarget=bd(),this.relativeTargetOrigin=bd(),dI(this.relativeTargetOrigin,this.target,a.target),ej(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}ah.value&&ey.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||z(this.parent.latestValues)||B(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let a=this.getLead(),b=!!this.resumingFrom||this!==a,c=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(c=!1),b&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(c=!1),this.resolvedRelativeTargetAt===al.timestamp&&(c=!1),c)return;let{layout:d,layoutId:e}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(d||e))return;ej(this.layoutCorrected,this.layout.layoutBox);let f=this.treeScale.x,g=this.treeScale.y;!function(a,b,c,d=!1){let e,f,g=c.length;if(g){b.x=b.y=1;for(let h=0;h<g;h++){f=(e=c[h]).projectionDelta;let{visualElement:g}=e.options;(!g||!g.props.style||"contents"!==g.props.style.display)&&(d&&e.options.layoutScroll&&e.scroll&&e!==e.root&&H(a,{x:-e.scroll.offset.x,y:-e.scroll.offset.y}),f&&(b.x*=f.x.scale,b.y*=f.y.scale,E(a,f)),d&&A(e.latestValues)&&H(a,e.latestValues))}b.x<1.0000000000001&&b.x>.999999999999&&(b.x=1),b.y<1.0000000000001&&b.y>.999999999999&&(b.y=1)}}(this.layoutCorrected,this.treeScale,this.path,b),a.layout&&!a.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(a.target=a.layout.layoutBox,a.targetWithTransforms=bd());let{target:h}=a;if(!h){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(ek(this.prevProjectionDelta.x,this.projectionDelta.x),ek(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),dF(this.projectionDelta,this.layoutCorrected,h,this.latestValues),this.treeScale.x===f&&this.treeScale.y===g&&ew(this.projectionDelta.x,this.prevProjectionDelta.x)&&ew(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",h)),ah.value&&ey.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(a=!0){if(this.options.visualElement?.scheduleRender(),a){let a=this.getStack();a&&a.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=bb(),this.projectionDelta=bb(),this.projectionDeltaWithTransform=bb()}setAnimationOrigin(a,b=!1){let c,d=this.snapshot,e=d?d.latestValues:{},f={...this.latestValues},g=bb();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!b;let h=bd(),i=(d?d.source:void 0)!==(this.layout?this.layout.source:void 0),j=this.getStack(),k=!j||j.members.length<=1,l=!!(i&&!k&&!0===this.options.crossfade&&!this.path.some(eS));this.animationProgress=0,this.mixTargetDelta=b=>{let d=b/1e3;if(eQ(g.x,a.x,d),eQ(g.y,a.y,d),this.setTargetDelta(g),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var j,m,n,o,p,q;dI(h,this.layout.layoutBox,this.relativeParent.layout.layoutBox),n=this.relativeTarget,o=this.relativeTargetOrigin,p=h,q=d,eR(n.x,o.x,p.x,q),eR(n.y,o.y,p.y,q),c&&(j=this.relativeTarget,m=c,es(j.x,m.x)&&es(j.y,m.y))&&(this.isProjectionDirty=!1),c||(c=bd()),ej(c,this.relativeTarget)}i&&(this.animationValues=f,function(a,b,c,d,e,f){e?(a.opacity=x(0,c.opacity??1,ef(d)),a.opacityExit=x(b.opacity??1,0,eg(d))):f&&(a.opacity=x(b.opacity??1,c.opacity??1,d));for(let e=0;e<eb;e++){let f=`border${ea[e]}Radius`,g=ee(b,f),h=ee(c,f);(void 0!==g||void 0!==h)&&(g||(g=0),h||(h=0),0===g||0===h||ed(g)===ed(h)?(a[f]=Math.max(x(ec(g),ec(h),d),0),(Q.test(h)||Q.test(g))&&(a[f]+="%")):a[f]=h)}(b.rotate||c.rotate)&&(a.rotate=x(b.rotate||0,c.rotate||0,d))}(f,e,this.latestValues,d,l,k)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=d},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(a){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&(ak(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=aj.update(()=>{d_.hasAnimatedSinceResize=!0,ce.layout++,this.motionValue||(this.motionValue=a5(0)),this.currentAnimation=function(a,b,c){let d=aZ(a)?a:a5(a);return d.start(dj("",d,b,c)),d.animation}(this.motionValue,[0,1e3],{...a,velocity:0,isSync:!0,onUpdate:b=>{this.mixTargetDelta(b),a.onUpdate&&a.onUpdate(b)},onStop:()=>{ce.layout--},onComplete:()=>{ce.layout--,a.onComplete&&a.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let a=this.getStack();a&&a.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let a=this.getLead(),{targetWithTransforms:b,target:c,layout:d,latestValues:e}=a;if(b&&c&&d){if(this!==a&&this.layout&&d&&eX(this.options.animationType,this.layout.layoutBox,d.layoutBox)){c=this.target||bd();let b=dD(this.layout.layoutBox.x);c.x.min=a.target.x.min,c.x.max=c.x.min+b;let d=dD(this.layout.layoutBox.y);c.y.min=a.target.y.min,c.y.max=c.y.min+d}ej(b,c),H(b,e),dF(this.projectionDeltaWithTransform,this.layoutCorrected,b,e)}}registerSharedNode(a,b){this.sharedNodes.has(a)||this.sharedNodes.set(a,new ex),this.sharedNodes.get(a).add(b);let c=b.options.initialPromotionConfig;b.promote({transition:c?c.transition:void 0,preserveFollowOpacity:c&&c.shouldPreserveFollowOpacity?c.shouldPreserveFollowOpacity(b):void 0})}isLead(){let a=this.getStack();return!a||a.lead===this}getLead(){let{layoutId:a}=this.options;return a&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:a}=this.options;return a?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:a}=this.options;if(a)return this.root.sharedNodes.get(a)}promote({needsReset:a,transition:b,preserveFollowOpacity:c}={}){let d=this.getStack();d&&d.promote(this,c),a&&(this.projectionDelta=void 0,this.needsReset=!0),b&&this.setOptions({transition:b})}relegate(){let a=this.getStack();return!!a&&a.relegate(this)}resetSkewAndRotation(){let{visualElement:a}=this.options;if(!a)return;let b=!1,{latestValues:c}=a;if((c.z||c.rotate||c.rotateX||c.rotateY||c.rotateZ||c.skewX||c.skewY)&&(b=!0),!b)return;let d={};c.z&&eB("z",a,d,this.animationValues);for(let b=0;b<ez.length;b++)eB(`rotate${ez[b]}`,a,d,this.animationValues),eB(`skew${ez[b]}`,a,d,this.animationValues);for(let b in a.render(),d)a.setStaticValue(b,d[b]),this.animationValues&&(this.animationValues[b]=d[b]);a.scheduleRender()}applyProjectionStyles(a,b){if(!this.instance||this.isSVG)return;if(!this.isVisible){a.visibility="hidden";return}let c=this.getTransformTemplate();if(this.needsReset){this.needsReset=!1,a.visibility="",a.opacity="",a.pointerEvents=bZ(b?.pointerEvents)||"",a.transform=c?c(this.latestValues,""):"none";return}let d=this.getLead();if(!this.projectionDelta||!this.layout||!d.target){this.options.layoutId&&(a.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,a.pointerEvents=bZ(b?.pointerEvents)||""),this.hasProjected&&!A(this.latestValues)&&(a.transform=c?c({},""):"none",this.hasProjected=!1);return}a.visibility="";let e=d.animationValues||d.latestValues;this.applyTransformsToTarget();let f=function(a,b,c){let d="",e=a.x.translate/b.x,f=a.y.translate/b.y,g=c?.z||0;if((e||f||g)&&(d=`translate3d(${e}px, ${f}px, ${g}px) `),(1!==b.x||1!==b.y)&&(d+=`scale(${1/b.x}, ${1/b.y}) `),c){let{transformPerspective:a,rotate:b,rotateX:e,rotateY:f,skewX:g,skewY:h}=c;a&&(d=`perspective(${a}px) ${d}`),b&&(d+=`rotate(${b}deg) `),e&&(d+=`rotateX(${e}deg) `),f&&(d+=`rotateY(${f}deg) `),g&&(d+=`skewX(${g}deg) `),h&&(d+=`skewY(${h}deg) `)}let h=a.x.scale*b.x,i=a.y.scale*b.y;return(1!==h||1!==i)&&(d+=`scale(${h}, ${i})`),d||"none"}(this.projectionDeltaWithTransform,this.treeScale,e);c&&(f=c(e,f)),a.transform=f;let{x:g,y:h}=this.projectionDelta;for(let b in a.transformOrigin=`${100*g.origin}% ${100*h.origin}% 0`,d.animationValues?a.opacity=d===this?e.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:e.opacityExit:a.opacity=d===this?void 0!==e.opacity?e.opacity:"":void 0!==e.opacityExit?e.opacityExit:0,by){if(void 0===e[b])continue;let{correct:c,applyTo:g,isCSSVariable:h}=by[b],i="none"===f?e[b]:c(e[b],d);if(g){let b=g.length;for(let c=0;c<b;c++)a[g[c]]=i}else h?this.options.visualElement.renderState.vars[b]=i:a[b]=i}this.options.layoutId&&(a.pointerEvents=d===this?bZ(b?.pointerEvents)||"":"none")}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(a=>a.currentAnimation?.stop()),this.root.nodes.forEach(eI),this.root.sharedNodes.clear()}}}function eD(a){a.updateLayout()}function eE(a){let b=a.resumeFrom?.snapshot||a.snapshot;if(a.isLead()&&a.layout&&b&&a.hasListeners("didUpdate")){let{layoutBox:c,measuredBox:d}=a.layout,{animationType:e}=a.options,f=b.source!==a.layout.source;"size"===e?dJ(a=>{let d=f?b.measuredBox[a]:b.layoutBox[a],e=dD(d);d.min=c[a].min,d.max=d.min+e}):eX(e,b.layoutBox,c)&&dJ(d=>{let e=f?b.measuredBox[d]:b.layoutBox[d],g=dD(c[d]);e.max=e.min+g,a.relativeTarget&&!a.currentAnimation&&(a.isProjectionDirty=!0,a.relativeTarget[d].max=a.relativeTarget[d].min+g)});let g=bb();dF(g,c,b.layoutBox);let h=bb();f?dF(h,a.applyTransform(d,!0),b.measuredBox):dF(h,c,b.layoutBox);let i=!er(g),j=!1;if(!a.resumeFrom){let d=a.getClosestProjectingParent();if(d&&!d.resumeFrom){let{snapshot:e,layout:f}=d;if(e&&f){let g=bd();dI(g,b.layoutBox,e.layoutBox);let h=bd();dI(h,c,f.layoutBox),eu(g,h)||(j=!0),d.options.layoutRoot&&(a.relativeTarget=h,a.relativeTargetOrigin=g,a.relativeParent=d)}}}a.notifyListeners("didUpdate",{layout:c,snapshot:b,delta:h,layoutDelta:g,hasLayoutChanged:i,hasRelativeLayoutChanged:j})}else if(a.isLead()){let{onExitComplete:b}=a.options;b&&b()}a.options.transition=void 0}function eF(a){ah.value&&ey.nodes++,a.parent&&(a.isProjecting()||(a.isProjectionDirty=a.parent.isProjectionDirty),a.isSharedProjectionDirty||(a.isSharedProjectionDirty=!!(a.isProjectionDirty||a.parent.isProjectionDirty||a.parent.isSharedProjectionDirty)),a.isTransformDirty||(a.isTransformDirty=a.parent.isTransformDirty))}function eG(a){a.isProjectionDirty=a.isSharedProjectionDirty=a.isTransformDirty=!1}function eH(a){a.clearSnapshot()}function eI(a){a.clearMeasurements()}function eJ(a){a.isLayoutDirty=!1}function eK(a){let{visualElement:b}=a.options;b&&b.getProps().onBeforeLayoutMeasure&&b.notify("BeforeLayoutMeasure"),a.resetTransform()}function eL(a){a.finishAnimation(),a.targetDelta=a.relativeTarget=a.target=void 0,a.isProjectionDirty=!0}function eM(a){a.resolveTargetDelta()}function eN(a){a.calcProjection()}function eO(a){a.resetSkewAndRotation()}function eP(a){a.removeLeadSnapshot()}function eQ(a,b,c){a.translate=x(b.translate,0,c),a.scale=x(b.scale,1,c),a.origin=b.origin,a.originPoint=b.originPoint}function eR(a,b,c,d){a.min=x(b.min,c.min,d),a.max=x(b.max,c.max,d)}function eS(a){return a.animationValues&&void 0!==a.animationValues.opacityExit}let eT={duration:.45,ease:[.4,0,.1,1]},eU=a=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(a),eV=eU("applewebkit/")&&!eU("chrome/")?Math.round:ae;function eW(a){a.min=eV(a.min),a.max=eV(a.max)}function eX(a,b,c){return"position"===a||"preserve-aspect"===a&&!(.2>=Math.abs(ev(b)-ev(c)))}function eY(a){return a!==a.root&&a.scroll?.wasRoot}let eZ=eC({attachResizeListener:(a,b)=>dz(a,"resize",b),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),e$={current:void 0},e_=eC({measureScroll:a=>({x:a.scrollLeft,y:a.scrollTop}),defaultParent:()=>{if(!e$.current){let a=new eZ({});a.mount(window),a.setOptions({layoutScroll:!0}),e$.current=a}return e$.current},resetTransform:(a,b)=>{a.style.transform=void 0!==b?b:"none"},checkIsScrollRoot:a=>"fixed"===window.getComputedStyle(a).position});function e0(a,b){let c=function(a,b,c){if(a instanceof EventTarget)return[a];if("string"==typeof a){let b=document,c=(void 0)??b.querySelectorAll(a);return c?Array.from(c):[]}return Array.from(a)}(a),d=new AbortController;return[c,{passive:!0,...b,signal:d.signal},()=>d.abort()]}function e1(a){return!("touch"===a.pointerType||dy.x||dy.y)}function e2(a,b,c){let{props:d}=a;a.animationState&&d.whileHover&&a.animationState.setActive("whileHover","Start"===c);let e=d["onHover"+c];e&&aj.postRender(()=>e(b,dB(b)))}class e3 extends du{mount(){let{current:a}=this.node;a&&(this.unmount=function(a,b,c={}){let[d,e,f]=e0(a,c),g=a=>{if(!e1(a))return;let{target:c}=a,d=b(c,a);if("function"!=typeof d||!c)return;let f=a=>{e1(a)&&(d(a),c.removeEventListener("pointerleave",f))};c.addEventListener("pointerleave",f,e)};return d.forEach(a=>{a.addEventListener("pointerenter",g,e)}),f}(a,(a,b)=>(e2(this.node,b,"Start"),a=>e2(this.node,a,"End"))))}unmount(){}}class e4 extends du{constructor(){super(...arguments),this.isActive=!1}onFocus(){let a=!1;try{a=this.node.current.matches(":focus-visible")}catch(b){a=!0}a&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=cc(dz(this.node.current,"focus",()=>this.onFocus()),dz(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}let e5=(a,b)=>!!b&&(a===b||e5(a,b.parentElement)),e6=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),e7=new WeakSet;function e8(a){return b=>{"Enter"===b.key&&a(b)}}function e9(a,b){a.dispatchEvent(new PointerEvent("pointer"+b,{isPrimary:!0,bubbles:!0}))}function fa(a){return dA(a)&&!(dy.x||dy.y)}function fb(a,b,c){let{props:d}=a;if(a.current instanceof HTMLButtonElement&&a.current.disabled)return;a.animationState&&d.whileTap&&a.animationState.setActive("whileTap","Start"===c);let e=d["onTap"+("End"===c?"":c)];e&&aj.postRender(()=>e(b,dB(b)))}class fc extends du{mount(){let{current:a}=this.node;a&&(this.unmount=function(a,b,c={}){let[d,e,f]=e0(a,c),g=a=>{let d=a.currentTarget;if(!fa(a))return;e7.add(d);let f=b(d,a),g=(a,b)=>{window.removeEventListener("pointerup",h),window.removeEventListener("pointercancel",i),e7.has(d)&&e7.delete(d),fa(a)&&"function"==typeof f&&f(a,{success:b})},h=a=>{g(a,d===window||d===document||c.useGlobalTarget||e5(d,a.target))},i=a=>{g(a,!1)};window.addEventListener("pointerup",h,e),window.addEventListener("pointercancel",i,e)};return d.forEach(a=>{(c.useGlobalTarget?window:a).addEventListener("pointerdown",g,e),d6(a)&&"offsetHeight"in a&&(a.addEventListener("focus",a=>((a,b)=>{let c=a.currentTarget;if(!c)return;let d=e8(()=>{if(e7.has(c))return;e9(c,"down");let a=e8(()=>{e9(c,"up")});c.addEventListener("keyup",a,b),c.addEventListener("blur",()=>e9(c,"cancel"),b)});c.addEventListener("keydown",d,b),c.addEventListener("blur",()=>c.removeEventListener("keydown",d),b)})(a,e)),e6.has(a.tagName)||-1!==a.tabIndex||a.hasAttribute("tabindex")||(a.tabIndex=0))}),f}(a,(a,b)=>(fb(this.node,b,"Start"),(a,{success:b})=>fb(this.node,a,b?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let fd=new WeakMap,fe=new WeakMap,ff=a=>{let b=fd.get(a.target);b&&b(a)},fg=a=>{a.forEach(ff)},fh={some:0,all:1};class fi extends du{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:a={}}=this.node.getProps(),{root:b,margin:c,amount:d="some",once:e}=a,f={root:b?b.current:void 0,rootMargin:c,threshold:"number"==typeof d?d:fh[d]};return function(a,b,c){let d=function({root:a,...b}){let c=a||document;fe.has(c)||fe.set(c,{});let d=fe.get(c),e=JSON.stringify(b);return d[e]||(d[e]=new IntersectionObserver(fg,{root:a,...b})),d[e]}(b);return fd.set(a,c),d.observe(a),()=>{fd.delete(a),d.unobserve(a)}}(this.node.current,f,a=>{let{isIntersecting:b}=a;if(this.isInView===b||(this.isInView=b,e&&!b&&this.hasEnteredView))return;b&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",b);let{onViewportEnter:c,onViewportLeave:d}=this.node.getProps(),f=b?c:d;f&&f(a)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:a,prevProps:b}=this.node;["amount","margin","root"].some(function({viewport:a={}},{viewport:b={}}={}){return c=>a[c]!==b[c]}(a,b))&&this.startObserver()}unmount(){}}let fj=function(a,b){if("undefined"==typeof Proxy)return b6;let c=new Map,d=(c,d)=>b6(c,d,a,b);return new Proxy((a,b)=>d(a,b),{get:(e,f)=>"create"===f?d:(c.has(f)||c.set(f,b6(f,void 0,a,b)),c.get(f))})}({animation:{Feature:dv},exit:{Feature:dx},inView:{Feature:fi},tap:{Feature:fc},focus:{Feature:e4},hover:{Feature:e3},pan:{Feature:d$},drag:{Feature:dY,ProjectionNode:e_,MeasureLayout:d4},layout:{ProjectionNode:e_,MeasureLayout:d4}},(a,b)=>bL(a)?new bJ(b):new bB(b,{allowProjection:a!==e.Fragment}))},3033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3413:(a,b,c)=>{"use strict";c.d(b,{default:()=>t});var d=c(687),e=c(2312),f=c(3210);let g=a=>{let b=a.replace(/^([A-Z])|[\s-_]+(\w)/g,(a,b,c)=>c?c.toUpperCase():b.toLowerCase());return b.charAt(0).toUpperCase()+b.slice(1)},h=(...a)=>a.filter((a,b,c)=>!!a&&""!==a.trim()&&c.indexOf(a)===b).join(" ").trim();var i={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let j=(0,f.forwardRef)(({color:a="currentColor",size:b=24,strokeWidth:c=2,absoluteStrokeWidth:d,className:e="",children:g,iconNode:j,...k},l)=>(0,f.createElement)("svg",{ref:l,...i,width:b,height:b,stroke:a,strokeWidth:d?24*Number(c)/Number(b):c,className:h("lucide",e),...!g&&!(a=>{for(let b in a)if(b.startsWith("aria-")||"role"===b||"title"===b)return!0})(k)&&{"aria-hidden":"true"},...k},[...j.map(([a,b])=>(0,f.createElement)(a,b)),...Array.isArray(g)?g:[g]])),k=(a,b)=>{let c=(0,f.forwardRef)(({className:c,...d},e)=>(0,f.createElement)(j,{ref:e,iconNode:b,className:h(`lucide-${g(a).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()}`,`lucide-${a}`,c),...d}));return c.displayName=g(a),c},l=k("car",[["path",{d:"M19 17h2c.6 0 1-.4 1-1v-3c0-.9-.7-1.7-1.5-1.9C18.7 10.6 16 10 16 10s-1.3-1.4-2.2-2.3c-.5-.4-1.1-.7-1.8-.7H5c-.6 0-1.1.4-1.4.9l-1.4 2.9A3.7 3.7 0 0 0 2 12v4c0 .6.4 1 1 1h2",key:"5owen"}],["circle",{cx:"7",cy:"17",r:"2",key:"u2ysq9"}],["path",{d:"M9 17h6",key:"r8uit2"}],["circle",{cx:"17",cy:"17",r:"2",key:"axvx0g"}]]),m=k("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]]),n=k("heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]]),o=k("map",[["path",{d:"M14.106 5.553a2 2 0 0 0 1.788 0l3.659-1.83A1 1 0 0 1 21 4.619v12.764a1 1 0 0 1-.553.894l-4.553 2.277a2 2 0 0 1-1.788 0l-4.212-2.106a2 2 0 0 0-1.788 0l-3.659 1.83A1 1 0 0 1 3 19.381V6.618a1 1 0 0 1 .553-.894l4.553-2.277a2 2 0 0 1 1.788 0z",key:"169xi5"}],["path",{d:"M15 5.764v15",key:"1pn4in"}],["path",{d:"M9 3.236v15",key:"1uimfh"}]]),p=k("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]]),q=k("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]);var r=c(8940);let s={Car:l,Shield:m,Heart:n,Map:o,Users:p,Calendar:q};function t(){return(0,d.jsx)("section",{className:"py-20 bg-neutral-50",children:(0,d.jsxs)("div",{className:"container mx-auto px-4",children:[(0,d.jsxs)(e.P.div,{className:"text-center mb-16",initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},viewport:{once:!0},transition:{duration:.6},children:[(0,d.jsx)("h2",{className:"text-3xl md:text-4xl font-heading font-bold text-neutral-900 mb-4",children:"Why Families Choose Blissful Trails"}),(0,d.jsx)("p",{className:"text-lg text-neutral-600 max-w-2xl mx-auto",children:"We understand Indian family travel needs and deliver experiences that prioritize safety, comfort, and convenience."})]}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:r.e.map((a,b)=>{let c=s[a.icon];return(0,d.jsxs)(e.P.div,{className:"bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow duration-300",initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},viewport:{once:!0},transition:{duration:.6,delay:.1*b},"data-testid":"value-prop-card",children:[(0,d.jsx)("div",{className:"w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mb-4",children:(0,d.jsx)(c,{className:"w-6 h-6 text-primary-600"})}),(0,d.jsx)("h3",{className:"text-xl font-semibold text-neutral-900 mb-3",children:a.title}),(0,d.jsx)("p",{className:"text-neutral-600 leading-relaxed",children:a.description})]},a.id)})})]})})}},3437:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(1369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\D\\\\Demo\\\\Travel Website\\\\blissful-trails-homepage\\\\src\\\\components\\\\sections\\\\ValuePropositionSection.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\D\\Demo\\Travel Website\\blissful-trails-homepage\\src\\components\\sections\\ValuePropositionSection.tsx","default")},3873:a=>{"use strict";a.exports=require("path")},4431:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>j,metadata:()=>i});var d=c(7413),e=c(2376),f=c.n(e),g=c(8726),h=c.n(g);c(1135);let i={title:"Create Next App",description:"Generated by create next app"};function j({children:a}){return(0,d.jsx)("html",{lang:"en",children:(0,d.jsx)("body",{className:`${f().variable} ${h().variable} antialiased`,children:a})})}},4660:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,5227,23)),Promise.resolve().then(c.t.bind(c,6346,23)),Promise.resolve().then(c.t.bind(c,7924,23)),Promise.resolve().then(c.t.bind(c,99,23)),Promise.resolve().then(c.t.bind(c,8243,23)),Promise.resolve().then(c.t.bind(c,8827,23)),Promise.resolve().then(c.t.bind(c,2763,23)),Promise.resolve().then(c.t.bind(c,7173,23)),Promise.resolve().then(c.bind(c,5587))},4804:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,6133,23)),Promise.resolve().then(c.t.bind(c,6444,23)),Promise.resolve().then(c.t.bind(c,6042,23)),Promise.resolve().then(c.t.bind(c,9477,23)),Promise.resolve().then(c.t.bind(c,9345,23)),Promise.resolve().then(c.t.bind(c,2089,23)),Promise.resolve().then(c.t.bind(c,6577,23)),Promise.resolve().then(c.t.bind(c,1307,23)),Promise.resolve().then(c.t.bind(c,4817,23))},5180:()=>{},6364:(a,b,c)=>{"use strict";c.d(b,{default:()=>h});var d=c(687),e=c(3210),f=c(2312),g=c(8940);function h(){let[a,b]=(0,e.useState)(!1),c=async()=>{b(!0),document.getElementById("booking-form")?.scrollIntoView({behavior:"smooth"}),b(!1)};return(0,d.jsxs)("section",{className:"relative min-h-screen flex items-center justify-center overflow-hidden",children:[(0,d.jsxs)("div",{className:"absolute inset-0 z-0",children:[(0,d.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-blue-400 via-green-400 to-green-600"}),(0,d.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-black/60 via-black/40 to-black/30"})]}),(0,d.jsxs)("div",{className:"relative z-10 container mx-auto px-4 text-center text-white",children:[(0,d.jsx)(f.P.h1,{className:"text-4xl md:text-6xl lg:text-7xl font-heading font-bold mb-6 leading-tight",initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8},children:g.$.headline}),(0,d.jsx)(f.P.p,{className:"text-xl md:text-2xl mb-8 max-w-2xl mx-auto",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.2},children:g.$.subheadline}),(0,d.jsx)(f.P.p,{className:"text-lg mb-12 max-w-3xl mx-auto opacity-90",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.4},children:g.$.description}),(0,d.jsxs)(f.P.div,{className:"flex flex-col sm:flex-row gap-4 justify-center mb-12",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.6},"data-testid":"cta-container",children:[(0,d.jsx)("button",{type:"button",onClick:c,disabled:a,className:"bg-primary-600 hover:bg-primary-700 text-white px-8 py-4 rounded-lg text-lg font-semibold transition-all duration-300 transform hover:scale-105 disabled:opacity-50 min-w-[200px]",children:a?"Loading...":g.$.primaryCTA}),(0,d.jsx)("button",{type:"button",onClick:()=>document.getElementById("packages")?.scrollIntoView({behavior:"smooth"}),className:"border-2 border-white text-white hover:bg-white hover:text-primary-600 px-8 py-4 rounded-lg text-lg font-semibold transition-all duration-300",children:g.$.secondaryCTA})]}),(0,d.jsx)(f.P.div,{className:"flex flex-wrap justify-center gap-6",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.8},children:g.$.trustBadges.map((a,b)=>(0,d.jsxs)("div",{className:"flex items-center gap-2 bg-white/20 backdrop-blur-sm px-4 py-2 rounded-full",children:[(0,d.jsx)("div",{className:"w-2 h-2 bg-secondary-500 rounded-full"}),(0,d.jsx)("span",{className:"text-sm font-medium",children:a})]},b))})]}),(0,d.jsx)(f.P.div,{className:"absolute bottom-8 left-1/2 transform -translate-x-1/2",animate:{y:[0,10,0]},transition:{duration:2,repeat:1/0},children:(0,d.jsx)("div",{className:"w-6 h-10 border-2 border-white rounded-full flex justify-center",children:(0,d.jsx)("div",{className:"w-1 h-3 bg-white rounded-full mt-2"})})})]})}},6439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},6713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},8354:a=>{"use strict";a.exports=require("util")},8734:(a,b,c)=>{Promise.resolve().then(c.bind(c,6364)),Promise.resolve().then(c.bind(c,3413))},8940:(a,b,c)=>{"use strict";c.d(b,{$:()=>d,e:()=>e});let d={headline:"Relax, We've Planned It All For You.",subheadline:"Curated trips. Clean cabs. Cozy stays.",description:"Experience hassle-free family travel with our hygiene-verified accommodations, multilingual guides, and 24/7 support across North Bengal and Sikkim.",primaryCTA:"Plan My Trip",secondaryCTA:"See Packages",trustBadges:["24x7 Support","Hygiene Verified","Family Approved"]},e=[{id:"verified-cabs",icon:"Car",title:"Verified Clean Cabs",description:"Sanitized vehicles with first aid kits and experienced drivers familiar with hill routes."},{id:"hygiene-rooms",icon:"Shield",title:"Hygiene Certified Stays",description:"Hand-picked accommodations with verified cleanliness standards and family-friendly amenities."},{id:"medical-support",icon:"Heart",title:"Emergency Medical Kit",description:"Every trip includes basic medical supplies and access to local healthcare networks."},{id:"custom-itinerary",icon:"Map",title:"Personalized Planning",description:"Customizable itineraries based on your family preferences, budget, and travel dates."},{id:"local-guides",icon:"Users",title:"Multilingual Guides",description:"Local experts who speak Hindi, Bengali, and English to ensure smooth communication."},{id:"flexible-booking",icon:"Calendar",title:"Flexible Cancellation",description:"Easy modifications and cancellations up to 48 hours before travel with full refund."}]},9121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var b=require("../webpack-runtime.js");b.C(a);var c=b.X(0,[985,400],()=>b(b.s=132));module.exports=c})();