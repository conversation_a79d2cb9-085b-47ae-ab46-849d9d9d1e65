export const HERO_CONTENT = {
  headline: "Relax, We've Planned It All For You.",
  subheadline: "Curated trips. Clean cabs. Cozy stays.",
  description: "Experience hassle-free family travel with our hygiene-verified accommodations, multilingual guides, and 24/7 support across North Bengal and Sikkim.",
  primaryCTA: "Plan My Trip",
  secondaryCTA: "See Packages",
  trustBadges: [
    "24x7 Support",
    "Hygiene Verified",
    "Family Approved"
  ]
} as const;

export const VALUE_PROPOSITIONS = [
  {
    id: 'verified-cabs',
    icon: 'Car',
    title: 'Verified Clean Cabs',
    description: 'Sanitized vehicles with first aid kits and experienced drivers familiar with hill routes.',
  },
  {
    id: 'hygiene-rooms',
    icon: 'Shield',
    title: 'Hygiene Certified Stays',
    description: 'Hand-picked accommodations with verified cleanliness standards and family-friendly amenities.',
  },
  {
    id: 'medical-support',
    icon: 'Heart',
    title: 'Emergency Medical Kit',
    description: 'Every trip includes basic medical supplies and access to local healthcare networks.',
  },
  {
    id: 'custom-itinerary',
    icon: 'Map',
    title: 'Personalized Planning',
    description: 'Customizable itineraries based on your family preferences, budget, and travel dates.',
  },
  {
    id: 'local-guides',
    icon: 'Users',
    title: 'Multilingual Guides',
    description: 'Local experts who speak Hindi, Bengali, and English to ensure smooth communication.',
  },
  {
    id: 'flexible-booking',
    icon: 'Calendar',
    title: 'Flexible Cancellation',
    description: 'Easy modifications and cancellations up to 48 hours before travel with full refund.',
  },
] as const;

export const PACKAGES_DATA = [
  {
    id: 'darjeeling-classic',
    category: 'Hill Stations',
    title: 'Darjeeling Classic Family Package',
    description: 'Experience the Queen of Hills with toy train rides, tea gardens, and stunning sunrise views from Tiger Hill.',
    image: '/images/packages/darjeeling-package.webp',
    duration: '4 Days / 3 Nights',
    priceRange: '₹15,000 - ₹25,000',
    rating: 4.8,
    reviewCount: 156,
    highlights: [
      'Toy Train Experience',
      'Tiger Hill Sunrise',
      'Tea Garden Tours',
      'Mall Road Shopping'
    ],
    tags: ['Family Friendly', 'Hill Station', 'Tea Gardens'],
    isPopular: true,
    region: 'Darjeeling'
  },
  {
    id: 'sikkim-adventure',
    category: 'Adventure Tours',
    title: 'Sikkim Adventure & Culture',
    description: 'Explore monasteries, pristine lakes, and mountain vistas in the Land of Thunderbolt.',
    image: '/images/packages/sikkim-package.webp',
    duration: '5 Days / 4 Nights',
    priceRange: '₹20,000 - ₹35,000',
    rating: 4.9,
    reviewCount: 89,
    highlights: [
      'Tsomgo Lake Visit',
      'Rumtek Monastery',
      'Gangtok City Tour',
      'Cable Car Rides'
    ],
    tags: ['Adventure', 'Culture', 'Mountains'],
    isPopular: false,
    region: 'Sikkim'
  },
  {
    id: 'dooars-wildlife',
    category: 'Dooars Wildlife',
    title: 'Dooars Wildlife Safari',
    description: 'Discover exotic wildlife in Jaldapara and Gorumara National Parks with comfortable jungle stays.',
    image: '/images/packages/dooars-package.webp',
    duration: '3 Days / 2 Nights',
    priceRange: '₹12,000 - ₹18,000',
    rating: 4.7,
    reviewCount: 124,
    highlights: [
      'Elephant Safari',
      'Bird Watching',
      'Jungle Lodge Stay',
      'Nature Walks'
    ],
    tags: ['Wildlife', 'Safari', 'Nature'],
    isPopular: true,
    region: 'Dooars'
  },
  {
    id: 'kalimpong-heritage',
    category: 'Cultural Experiences',
    title: 'Kalimpong Heritage Trail',
    description: 'Immerse in local culture with monastery visits, handicraft workshops, and scenic valley views.',
    image: '/images/packages/kalimpong-package.webp',
    duration: '3 Days / 2 Nights',
    priceRange: '₹10,000 - ₹16,000',
    rating: 4.6,
    reviewCount: 67,
    highlights: [
      'Zang Dhok Palri Monastery',
      'Flower Nurseries',
      'Handicraft Centers',
      'Valley Views'
    ],
    tags: ['Heritage', 'Culture', 'Peaceful'],
    isPopular: false,
    region: 'Kalimpong'
  },
  {
    id: 'complete-north-bengal',
    category: 'Family Getaways',
    title: 'Complete North Bengal Experience',
    description: 'Comprehensive tour covering Darjeeling, Sikkim, and Dooars for the ultimate family adventure.',
    image: '/images/packages/north-bengal-package.webp',
    duration: '8 Days / 7 Nights',
    priceRange: '₹35,000 - ₹55,000',
    rating: 4.9,
    reviewCount: 203,
    highlights: [
      'Multi-destination Tour',
      'All Major Attractions',
      'Comfortable Transportation',
      'Expert Local Guides'
    ],
    tags: ['Comprehensive', 'Family', 'Multi-destination'],
    isPopular: true,
    region: 'Multi-region'
  },
  {
    id: 'mirik-peaceful',
    category: 'Family Getaways',
    title: 'Mirik Peaceful Retreat',
    description: 'Serene lakeside retreat perfect for families seeking tranquility away from crowded destinations.',
    image: '/images/packages/mirik-package.webp',
    duration: '2 Days / 1 Night',
    priceRange: '₹8,000 - ₹12,000',
    rating: 4.5,
    reviewCount: 45,
    highlights: [
      'Sumendu Lake Boating',
      'Orange Gardens',
      'Peaceful Environment',
      'Family Activities'
    ],
    tags: ['Peaceful', 'Lake', 'Short Trip'],
    isPopular: false,
    region: 'Mirik'
  }
] as const;

export const PACKAGE_CATEGORIES = [
  'All Packages',
  'Hill Stations',
  'Dooars Wildlife',
  'Family Getaways',
  'Adventure Tours',
  'Cultural Experiences'
] as const;

export const PACKAGE_FILTERS = {
  regions: ['All Regions', 'Darjeeling', 'Sikkim', 'Dooars', 'Kalimpong', 'Mirik', 'Multi-region'],
  duration: ['Any Duration', '1-2 Days', '3-4 Days', '5-6 Days', '7+ Days'],
  budget: ['Any Budget', 'Under ₹15,000', '₹15,000 - ₹25,000', '₹25,000 - ₹40,000', 'Above ₹40,000']
} as const;

export const HOW_IT_WORKS_STEPS = [
  {
    id: 'select-package',
    step: 1,
    title: 'Select Package',
    description: 'Browse our curated travel packages or tell us your preferences for a custom itinerary.',
    icon: 'Search',
    details: [
      'Browse 50+ pre-designed packages',
      'Filter by destination, budget & duration',
      'Read reviews from verified travelers',
      'Compare package features & inclusions'
    ],
    interactiveElement: 'Package Selector',
    estimatedTime: '5-10 minutes'
  },
  {
    id: 'customize-trip',
    step: 2,
    title: 'Add Food & Stops',
    description: 'Customize your journey with preferred meals, additional stops, and special requirements.',
    icon: 'Settings',
    details: [
      'Choose meal preferences (Veg/Non-veg/Jain)',
      'Add scenic stops & photo points',
      'Select accommodation upgrades',
      'Include special activities or experiences'
    ],
    interactiveElement: 'Customization Options',
    estimatedTime: '10-15 minutes'
  },
  {
    id: 'confirm-booking',
    step: 3,
    title: 'Confirm Booking',
    description: 'Review your itinerary, make secure payment, and receive instant confirmation.',
    icon: 'CreditCard',
    details: [
      'Review complete itinerary & pricing',
      'Secure payment via UPI/Cards/Net Banking',
      'Instant booking confirmation via SMS/Email',
      'Download detailed travel documents'
    ],
    interactiveElement: 'Secure Payment Gateway',
    estimatedTime: '5 minutes'
  },
  {
    id: 'travel-worry-free',
    step: 4,
    title: 'Travel Worry-Free',
    description: 'Enjoy your journey with 24/7 support, real-time updates, and emergency assistance.',
    icon: 'Shield',
    details: [
      '24/7 WhatsApp support during travel',
      'Real-time driver & vehicle tracking',
      'Emergency medical assistance',
      'Instant rebooking for weather disruptions'
    ],
    interactiveElement: 'Real-time Support',
    estimatedTime: 'Throughout your journey'
  }
] as const;

export const HOW_IT_WORKS_CONTENT = {
  headline: 'How It Works',
  subheadline: 'Your Journey to Blissful Travel in 4 Simple Steps',
  description: 'From selection to return, we\'ve streamlined every aspect of your travel experience. Our proven process ensures hassle-free planning and unforgettable memories.',
  ctaText: 'Start Planning Now',
  trustMessage: 'Trusted by 10,000+ families across India'
} as const;
