export default function StyleTest() {
  return (
    <div className="p-8 bg-primary-50 border-2 border-primary-200 rounded-lg">
      <h2 className="text-2xl font-heading font-bold text-primary-600 mb-4">
        Tailwind CSS Test
      </h2>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-white p-4 rounded-lg shadow-md">
          <h3 className="font-semibold text-neutral-900 mb-2">Primary Colors</h3>
          <div className="flex gap-2">
            <div className="w-8 h-8 bg-primary-500 rounded"></div>
            <div className="w-8 h-8 bg-primary-600 rounded"></div>
            <div className="w-8 h-8 bg-primary-700 rounded"></div>
          </div>
        </div>
        <div className="bg-white p-4 rounded-lg shadow-md">
          <h3 className="font-semibold text-neutral-900 mb-2">Secondary Colors</h3>
          <div className="flex gap-2">
            <div className="w-8 h-8 bg-secondary-500 rounded"></div>
            <div className="w-8 h-8 bg-secondary-600 rounded"></div>
            <div className="w-8 h-8 bg-secondary-700 rounded"></div>
          </div>
        </div>
        <div className="bg-white p-4 rounded-lg shadow-md">
          <h3 className="font-semibold text-neutral-900 mb-2">Accent Colors</h3>
          <div className="flex gap-2">
            <div className="w-8 h-8 bg-accent-500 rounded"></div>
            <div className="w-8 h-8 bg-accent-600 rounded"></div>
            <div className="w-8 h-8 bg-accent-700 rounded"></div>
          </div>
        </div>
      </div>
      <div className="mt-6">
        <button className="bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors duration-300 mr-4">
          Primary Button
        </button>
        <button className="bg-secondary-600 hover:bg-secondary-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors duration-300">
          Secondary Button
        </button>
      </div>
      <div className="mt-4">
        <p className="text-neutral-600">
          If you can see styled colors and buttons above, Tailwind CSS is working correctly!
        </p>
      </div>
    </div>
  );
}
